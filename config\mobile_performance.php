<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Mobile Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration settings for mobile Nouraniyah reporting performance
    | optimizations. These settings control caching, query optimization,
    | and mobile-specific features.
    |
    */

    'cache' => [
        'enabled' => env('MOBILE_CACHE_ENABLED', true),
        'ttl' => [
            'short' => env('MOBILE_CACHE_TTL_SHORT', 900),    // 15 minutes
            'medium' => env('MOBILE_CACHE_TTL_MEDIUM', 3600), // 1 hour
            'long' => env('MOBILE_CACHE_TTL_LONG', 86400),    // 24 hours
        ],
        'keys' => [
            'class_data' => 'preloaded_class_data_{class_id}_{date}',
            'student_lessons' => 'student_lessons_{student_id}_{class_id}_{date}',
            'attendance_options' => 'attendance_options_mobile',
            'evaluation_options' => 'evaluation_options_class_{class_id}',
        ],
    ],

    'performance' => [
        'monitoring_enabled' => env('MOBILE_PERFORMANCE_MONITORING', true),
        'slow_query_threshold' => env('MOBILE_SLOW_QUERY_THRESHOLD', 2.0), // seconds
        'memory_limit_warning' => env('MOBILE_MEMORY_WARNING', 64), // MB
        'max_students_per_page' => env('MOBILE_MAX_STUDENTS', 50),
        'lazy_loading_enabled' => env('MOBILE_LAZY_LOADING', true),
    ],

    'database' => [
        'query_optimization' => env('MOBILE_DB_OPTIMIZATION', true),
        'connection_pooling' => env('MOBILE_DB_POOLING', true),
        'query_cache_size' => env('MOBILE_QUERY_CACHE_SIZE', 67108864), // 64MB
        'tmp_table_size' => env('MOBILE_TMP_TABLE_SIZE', 67108864),     // 64MB
    ],

    'ui' => [
        'accordion_auto_collapse' => env('MOBILE_AUTO_COLLAPSE', true),
        'search_debounce_ms' => env('MOBILE_SEARCH_DEBOUNCE', 300),
        'animation_duration' => env('MOBILE_ANIMATION_DURATION', 200),
        'touch_target_size' => env('MOBILE_TOUCH_TARGET', 44), // pixels
    ],

    'features' => [
        'offline_support' => env('MOBILE_OFFLINE_SUPPORT', false),
        'push_notifications' => env('MOBILE_PUSH_NOTIFICATIONS', false),
        'background_sync' => env('MOBILE_BACKGROUND_SYNC', false),
        'progressive_web_app' => env('MOBILE_PWA_ENABLED', false),
    ],

    'security' => [
        'rate_limiting' => [
            'enabled' => env('MOBILE_RATE_LIMITING', true),
            'requests_per_minute' => env('MOBILE_RATE_LIMIT', 60),
        ],
        'csrf_protection' => env('MOBILE_CSRF_PROTECTION', true),
        'secure_headers' => env('MOBILE_SECURE_HEADERS', true),
    ],

    'logging' => [
        'performance_logs' => env('MOBILE_PERFORMANCE_LOGS', true),
        'error_tracking' => env('MOBILE_ERROR_TRACKING', true),
        'user_analytics' => env('MOBILE_USER_ANALYTICS', false),
        'retention_days' => env('MOBILE_LOG_RETENTION', 30),
    ],
];
