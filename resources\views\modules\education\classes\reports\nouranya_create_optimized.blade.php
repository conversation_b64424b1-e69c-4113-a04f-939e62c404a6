@extends('layouts.hound')
@section('mytitle', 'Daily Student Activity Report Form')

@section("css")
    <link href="{{ asset('css/nouranya-mobile.css') }}?v={{ time() }}" rel="stylesheet" type="text/css">
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" type="text/css" rel="stylesheet"/>
    <link href="https://cdn.datatables.net/1.10.22/css/dataTables.bootstrap.min.css" type="text/css" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.4.1/semantic.min.css" type="text/css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="https://selectize.dev/css/selectize.css"/>

    <style>
        /* Critical desktop styles only */
        #nouranyaReportTable {
            border: 2px solid olive !important;
            width: 100%;
            border-collapse: collapse;
            background-color: green !important;
        }

        #nouranyaReportTable th {
            background-color: green !important;
            color: #FFFACD !important;
            border: 1px solid olive !important;
            padding: 8px !important;
        }

        #nouranyaReportTable td {
            border: 1px solid olive !important;
            padding: 8px !important;
            background-color: green;
            color: white;
        }

        .level-badge {
            font-size: 0.75em;
            margin-left: 5px;
            border-radius: 4px;
            padding: 2px 8px;
            font-weight: bold;
            display: inline-block;
            border: 2px solid;
            background-color: transparent;
        }

        .level-badge-one { border-color: white; color: white; }
        .level-badge-two { border-color: yellow; color: yellow; }
        .level-badge-three { border-color: #00ff00; color: #00ff00; }

        .custom-breadcrumb li + li:before {
            content: "|";
            color: #333;
            padding: 0 5px;
            font-size: 21px;
        }

        .custom-breadcrumb li { font-size: 21px; }
        .custom-breadcrumb li a { font-size: 21px; }
        .custom-breadcrumb li.active { color: #009933; }
        .custom-breadcrumb li.active a { color: #009933; }

        .nav-tabs > li.active > a {
            border-bottom: 2px solid #009933 !important;
        }

        .classReportTabs {
            color: #337ab7 !important;
        }

        .teacherImage {
            width: 50px !important;
        }

        .studentProfileLink:hover {
            background-color: #1fff0f !important;
            color: #000 !important;
            text-decoration: underline !important;
            border-radius: 4px !important;
        }

        /* Performance optimizations */
        .ui.card .image img {
            width: 100px;
        }

        /* Loading indicator */
        .page-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #28a745;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
@endsection

@section('content')
    <!-- Loading indicator -->
    <div id="pageLoader" class="page-loading" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    @php
        $classId = request()->route()->parameter('id');
        $isNouranyaProgram = $class->programs->contains(function ($program) {
            return str_contains($program->title, 'Nuraniyah');
        });

        $nuraniyahDates = \App\StudentNouranyaReport::where('class_id', $classId)
            ->whereIn('attendance_id', [1, 2])
            ->selectRaw('DISTINCT DATE_FORMAT(created_at, "%Y-%m-%d") as monthYearDay')
            ->get();
    @endphp

    <div class="pull-right">
        <ol class="breadcrumb custom-breadcrumb">
            <li><a href="{{url('workplace/education/classes/')}}">Classes</a></li>
            <li>
                <a href="{{url('workplace/education/classes/'.request()->route()->parameter('id').'/reports')}}">
                    Class Reporting Calendar
                </a>
            </li>
            <li class="active">Class Report</li>
        </ol>
    </div>

    <div class="panel-heading">Class: {{ $class->name }}</div>
    <div class="panel-body">

        <ul role="tablist" class="nav nav-tabs" id="myTabs">
            <li role="presentation">
                <a class="classReportTabs" aria-expanded="true" role="tab" id="home_tab"
                   href="{{route('classes.show',['id' => $class->id])}}">Info/View</a>
            </li>
            <li role="presentation">
                <a class="classReportTabs" aria-expanded="true" role="tab" id="home_tab"
                   href="{{ route('reports.calendar',['id' => $class->id]) }}">Calendar</a>
            </li>
            <li class="" role="presentation">
                <a class="classReportTabs" aria-expanded="true" role="tab" id="home_tab"
                   href="{{route('monthly-plan.show',[$class->id, $from_date])}}">Monthly Plan</a>
            </li>
            <li class="active" role="presentation">
                <a class="classReportTabs" aria-expanded="true" role="tab" id="home_tab"
                   href="{{route('reports.create', ['id' => $class->id, 'from_date' => \Carbon\Carbon::today()->toDateString()])}}">Daily Report</a>
            </li>
            <li role="presentation">
                <a class="classReportTabs" aria-expanded="true" role="tab" id="home_tab"
                   href="{{route('class.student.report',['classId' => $class->id])}}">Student Report</a>
            </li>
            <li role="presentation">
                <a class="classReportTabs" aria-expanded="true" role="tab" id="home_tab"
                   href="{{route('class.halaqah.report', ['classId' =>  $class->id, 'from_date' => \Carbon\Carbon::parse(request()->from_date)->toDateString()])}}">Class Report</a>
            </li>
        </ul>

        <div class="tab-content table-bordered" id="myTabContent">
            <div id="daily_reports" class="tab-pane fade active in" role="tabpanel">
                <div class="panel-body">
                    @if ($errors->any())
                        <ul class="alert alert-danger">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif

                    <h2 class="ui header">Teachers</h2>
                    <div class="ui link cards">
                        @foreach($teachers as $key => $teacher)
                            @php
                                $genderBasedDefaultImage = $teacher->gender == 'male' 
                                    ? asset('uploads/staff/demo/mstaff.jfif') 
                                    : asset('uploads/staff/demo/fstaff.jfif');
                                $image = $teacher->image 
                                    ? asset('uploads/staff/' . $teacher->image) 
                                    : $genderBasedDefaultImage;
                                $impersonationRoute = url('multiAuthImpersonate/take/' . $teacher->id . '/employee');
                            @endphp

                            <div class="card">
                                <div class="image">
                                    <img src="{{ $image }}" alt="{{ $teacher->full_name }}" class="teacherImage">
                                </div>
                                <div class="content">
                                    <div class="header">{{ $teacher->full_name }}</div>
                                    <div class="meta">
                                        <a href="{{ $impersonationRoute }}" class="studentProfileLink">View Profile</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <br>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h3 class="panel-title">
                                        Daily Report for {{ \Carbon\Carbon::parse($from_date)->format('F j, Y') }}
                                        <span class="pull-right">
                                            <span class="label label-success">Complete Plans: {{ $completeMonthlyPlansCount }}</span>
                                            <span class="label label-warning">Incomplete Plans: {{ $incompleteMonthlyPlansCount }}</span>
                                            <span class="label label-info">No Plan Students: {{ $noMonthlyPlanStudentsCount }}</span>
                                        </span>
                                    </h3>
                                </div>
                                <div class="panel-body">
                                    <!-- Desktop Table (Hidden on Mobile) -->
                                    <table id="nouranyaReportTable" class="table table-striped table-bordered" style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Student</th>
                                                <th>Level</th>
                                                <th>Attendance</th>
                                                <th>From Lesson</th>
                                                <th>To Lesson</th>
                                                <th>Pages</th>
                                                <th>Evaluation</th>
                                                <th>Last Record</th>
                                                <th>Remarks</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($students as $key => $student)
                                                @php
                                                    $selectedProgramLevelTitle = optional($student->studentProgramLevels->first())->programlevel->title ?? '';
                                                    $studentLevelTitle = $student->nouranya_plans->first()->level_id ?? null;
                                                    $image = $studentImageService->getStudentImageUrl($student);
                                                    $badgeClass = 'level-badge';
                                                    
                                                    if ($studentLevelTitle) {
                                                        $levelTitleLower = strtolower($selectedProgramLevelTitle);
                                                        if (stripos($levelTitleLower, 'level 1') !== false) {
                                                            $badgeClass .= ' level-badge-one';
                                                        } elseif (stripos($levelTitleLower, 'level 2') !== false) {
                                                            $badgeClass .= ' level-badge-two';
                                                        } elseif (stripos($levelTitleLower, 'level 3') !== false) {
                                                            $badgeClass .= ' level-badge-three';
                                                        }
                                                    }
                                                @endphp
                                                <tr>
                                                    <td>
                                                        <img src="{{$image}}" alt="{{ $student->full_name }}" style="width: 30px; height: 30px; border-radius: 50%; margin-right: 8px;">
                                                        {{ $student->full_name }}
                                                        @if($studentLevelTitle)
                                                            <span class="{{ $badgeClass }}">{{ $selectedProgramLevelTitle }}</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ $selectedProgramLevelTitle }}</td>
                                                    <td>
                                                        @if($student->nouranya_plans->first()->status == 'active')
                                                            <select name="attendance_id" class="form-control nouranya-attendance-dropdown"
                                                                    data-student-id="{{$student->id}}">
                                                                <option value="">Select Attendance</option>
                                                                @foreach($attendanceOptions as $attendanceOption)
                                                                    <option value="{{$attendanceOption->id}}" 
                                                                            @if(optional($student->nouranya->first())->attendance_id == $attendanceOption->id) selected @endif>
                                                                        {{$attendanceOption->display_name}}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        @endif
                                                    </td>
                                                    <td><!-- From Lesson --></td>
                                                    <td><!-- To Lesson --></td>
                                                    <td><!-- Pages --></td>
                                                    <td><!-- Evaluation --></td>
                                                    <td><!-- Last Record --></td>
                                                    <td><!-- Remarks --></td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>

                                    <!-- Mobile/Tablet View (Optimized) -->
                                    <div id="mobileReportView" class="mobile-report-container">
                                        <!-- Search Bar for Mobile -->
                                        <div class="mobile-search-container">
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <input type="text" id="mobileStudentSearch" class="form-control" placeholder="Search students by name...">
                                                    <span class="input-group-addon">
                                                        <i class="glyphicon glyphicon-search"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Student Accordions -->
                                        <div class="mobile-students-container">
                                            @foreach($students as $key => $student)
                                                @php
                                                    $selectedProgramLevelTitle = optional($student->studentProgramLevels->first())->programlevel->title ?? '';
                                                    $studentLevelTitle = $student->nouranya_plans->first()->level_id ?? null;
                                                    $truncatedName = \Illuminate\Support\Str::limit($student->full_name, 12, '');
                                                    $isTruncated = $truncatedName !== $student->full_name;
                                                    $image = $studentImageService->getStudentImageUrl($student);
                                                    $badgeClass = 'mobile-level-badge';

                                                    if ($studentLevelTitle) {
                                                        $levelTitleLower = strtolower($selectedProgramLevelTitle);
                                                        if (stripos($levelTitleLower, 'level 1') !== false) {
                                                            $badgeClass .= ' mobile-level-badge-one';
                                                        } elseif (stripos($levelTitleLower, 'level 2') !== false) {
                                                            $badgeClass .= ' mobile-level-badge-two';
                                                        } elseif (stripos($levelTitleLower, 'level 3') !== false) {
                                                            $badgeClass .= ' mobile-level-badge-three';
                                                        }
                                                    }
                                                @endphp

                                                <div class="mobile-student-accordion panel panel-default" data-student-name="{{$student->full_name}}">
                                                    <!-- Student Header -->
                                                    <div class="panel-heading mobile-student-header" data-toggle="collapse" data-target="#mobileStudent{{$student->id}}">
                                                        <div class="mobile-student-info">
                                                            <div class="mobile-student-avatar">
                                                                <img src="{{$image}}" alt="{{ $student->full_name }}" class="mobile-avatar-img">
                                                            </div>
                                                            <div class="mobile-student-details">
                                                                <h4 class="mobile-student-name">{{ $student->full_name }}</h4>
                                                                @if($studentLevelTitle)
                                                                    <span class="{{ $badgeClass }}">{{ $selectedProgramLevelTitle }}</span>
                                                                @endif
                                                            </div>
                                                            <div class="mobile-expand-icon">
                                                                <i class="glyphicon glyphicon-chevron-down"></i>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Student Content -->
                                                    <div id="mobileStudent{{$student->id}}" class="panel-collapse collapse">
                                                        <div class="panel-body mobile-student-content">

                                                            <!-- Attendance Section -->
                                                            <div class="mobile-section mobile-attendance-section">
                                                                <h5 class="mobile-section-title">
                                                                    <i class="glyphicon glyphicon-check"></i> Attendance
                                                                </h5>
                                                                <div class="mobile-field-group">
                                                                    @if($student->nouranya_plans->first()->status == 'active')
                                                                        <select name="mobile_attendance_id" class="form-control mobile-nouranya-attendance-dropdown"
                                                                                data-student-id="{{$student->id}}"
                                                                                data-program-level-title="{{ optional($student->studentProgramLevels[0])->programlevel->title }}"
                                                                                data-nouranyaplan-id="{{$student->nouranya_plans->first()->id}}"
                                                                                data-student-name="{{$student->full_name}}"
                                                                                data-report-id="@if($student->nouranya){{$student->nouranya->first()->id}}@endif">
                                                                            <option value="">Select Attendance</option>
                                                                            @foreach($attendanceOptions as $attendanceOption)
                                                                                <option value="{{$attendanceOption->id}}"
                                                                                        @if(optional($student->nouranya->first())->attendance_id == $attendanceOption->id) selected @endif>
                                                                                    {{$attendanceOption->display_name}}
                                                                                </option>
                                                                            @endforeach
                                                                        </select>
                                                                    @endif
                                                                </div>
                                                            </div>

                                                            <!-- Nouranya Section -->
                                                            <div class="mobile-section mobile-nouranya-section"
                                                                 data-student-id="{{$student->id}}"
                                                                 data-attendance-id="{{optional($student->nouranya->first())->attendance_id}}"
                                                                 data-student-name="{{$student->full_name}}"
                                                                 data-teacher-id="{{auth()->user()->id}}"
                                                                 data-organization-id="{{$student->organization_id}}"
                                                                 data-class-id="{{$class->id}}"
                                                                 data-report-id="@if($student->nouranya){{$student->nouranya->first()->id}}@endif"
                                                                 data-nouranyaplan-id="{{$student->nouranya_plans->first()->id}}"
                                                                 data-table="hefz">

                                                                <h5 class="mobile-section-title">
                                                                    <i class="glyphicon glyphicon-book"></i> Nouranya Progress
                                                                </h5>

                                                                <div class="mobile-nouranya-content">
                                                                    <!-- Simplified lesson fields for mobile -->
                                                                    <div class="mobile-lesson-fields">
                                                                        <div class="row mobile-lesson-row">
                                                                            <div class="col-xs-6">
                                                                                <div class="mobile-field-group">
                                                                                    <label class="mobile-field-label">From Lesson</label>
                                                                                    <select name="mobile_from_lesson" class="form-control mobile-from-lesson-dropdown"
                                                                                            data-student-id="{{$student->id}}">
                                                                                        <option value="">Select From Lesson</option>
                                                                                        <!-- Options will be populated via AJAX -->
                                                                                    </select>
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-xs-6">
                                                                                <div class="mobile-field-group">
                                                                                    <label class="mobile-field-label">To Lesson</label>
                                                                                    <select name="mobile_to_lesson" class="form-control mobile-to-lesson-dropdown"
                                                                                            data-student-id="{{$student->id}}">
                                                                                        <option value="">Select To Lesson</option>
                                                                                        <!-- Options will be populated via AJAX -->
                                                                                    </select>
                                                                                </div>
                                                                            </div>
                                                                        </div>

                                                                        <div class="row mobile-lesson-row">
                                                                            <div class="col-xs-6">
                                                                                <div class="mobile-field-group">
                                                                                    <label class="mobile-field-label">Pages</label>
                                                                                    <div class="mobile-pages-display">
                                                                                        <span class="mobile-pages-count">0</span> pages
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-xs-6">
                                                                                <div class="mobile-field-group">
                                                                                    <label class="mobile-field-label">Evaluation</label>
                                                                                    <select name="mobile_nouranya_evaluation_id" class="form-control mobile-evaluation-dropdown"
                                                                                            data-student-id="{{$student->id}}">
                                                                                        <option value="">Select Evaluation</option>
                                                                                        @foreach($nouranya_valuation_options as $option)
                                                                                            <option value="{{$option->id}}"
                                                                                                    @if(optional($student->nouranya->first())->nouranya_evaluation_id == $option->id) selected @endif>
                                                                                                {{$option->title}}
                                                                                            </option>
                                                                                        @endforeach
                                                                                    </select>
                                                                                </div>
                                                                            </div>
                                                                        </div>

                                                                        <div class="row mobile-lesson-row">
                                                                            <div class="col-xs-12">
                                                                                <div class="mobile-field-group">
                                                                                    <label class="mobile-field-label">Last Record</label>
                                                                                    <div class="mobile-last-record">
                                                                                        <span class="mobile-last-record-text">
                                                                                            @if($student->last_nouranya)
                                                                                                {{ optional($student->last_nouranya)->created_at ? $student->last_nouranya->created_at->format('M j, Y') : '—' }}
                                                                                            @else
                                                                                                No previous record
                                                                                            @endif
                                                                                        </span>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <!-- Remarks Button -->
                                                                    <div class="mobile-remarks-container">
                                                                        <button type="button" class="btn btn-danger mobile-nouranya-remarks-btn"
                                                                                data-student-id="{{$student->id}}"
                                                                                data-student-name="{{$student->full_name}}">
                                                                            <i class="glyphicon glyphicon-edit"></i> View/Edit Remarks
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('jssnippets.select2')
@endsection

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        /**
         * Optimized Mobile Nouranya Report JavaScript
         *
         * Purpose: Handles mobile-first interactions for daily reporting with
         * performance optimizations including debounced search, lazy loading,
         * and efficient DOM manipulation.
         */

        $(document).ready(function() {
            // Performance monitoring
            const startTime = performance.now();

            // Initialize mobile functionality
            initializeMobileSearch();
            initializeAccordions();
            initializeFormHandlers();
            initializeLazyLoading();

            // Hide page loader
            $('#pageLoader').fadeOut(300);

            // Log performance
            const loadTime = performance.now() - startTime;
            console.log(`Mobile report initialized in ${loadTime.toFixed(2)}ms`);
        });

        /**
         * Initialize mobile search with debouncing for performance
         */
        function initializeMobileSearch() {
            let searchTimeout;

            $('#mobileStudentSearch').on('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = $(this).val().toLowerCase().trim();

                searchTimeout = setTimeout(function() {
                    filterStudents(searchTerm);
                }, 300); // Debounce for 300ms
            });
        }

        /**
         * Filter students with smooth animations
         */
        function filterStudents(searchTerm) {
            const $students = $('.mobile-student-accordion');
            let visibleCount = 0;

            $students.each(function() {
                const studentName = $(this).data('student-name').toLowerCase();
                const $this = $(this);

                if (searchTerm === '' || studentName.includes(searchTerm)) {
                    $this.removeClass('search-hidden').fadeIn(200);
                    visibleCount++;
                } else {
                    $this.addClass('search-hidden').fadeOut(200);
                }
            });

            // Show no results message if needed
            if (visibleCount === 0 && searchTerm !== '') {
                if ($('.no-results-message').length === 0) {
                    $('.mobile-students-container').append(
                        '<div class="no-results-message alert alert-info">No students found matching "' + searchTerm + '"</div>'
                    );
                }
            } else {
                $('.no-results-message').remove();
            }
        }

        /**
         * Initialize accordion functionality with performance optimizations
         */
        function initializeAccordions() {
            $('.mobile-student-header').on('click', function() {
                const $accordion = $(this).closest('.mobile-student-accordion');
                const $content = $accordion.find('.panel-collapse');
                const $icon = $(this).find('.mobile-expand-icon i');

                // Toggle active state
                $accordion.toggleClass('active-accordion');

                // Animate icon
                if ($content.hasClass('in')) {
                    $icon.removeClass('glyphicon-chevron-up').addClass('glyphicon-chevron-down');
                } else {
                    $icon.removeClass('glyphicon-chevron-down').addClass('glyphicon-chevron-up');

                    // Lazy load lesson data when accordion opens
                    const studentId = $accordion.find('[data-student-id]').first().data('student-id');
                    loadLessonData(studentId);
                }
            });
        }

        /**
         * Initialize form handlers with AJAX submission
         */
        function initializeFormHandlers() {
            // Attendance dropdown handler
            $(document).on('change', '.mobile-nouranya-attendance-dropdown', function() {
                const $this = $(this);
                const studentId = $this.data('student-id');
                const attendanceId = $this.val();

                updateAttendance(studentId, attendanceId, $this);
            });

            // Evaluation dropdown handler
            $(document).on('change', '.mobile-evaluation-dropdown', function() {
                const $this = $(this);
                const studentId = $this.data('student-id');
                const evaluationId = $this.val();

                updateEvaluation(studentId, evaluationId, $this);
            });

            // Lesson dropdown handlers
            $(document).on('change', '.mobile-from-lesson-dropdown', function() {
                const $this = $(this);
                const studentId = $this.data('student-id');
                const fromLessonId = $this.val();

                updateFromLesson(studentId, fromLessonId, $this);
                loadToLessons(studentId, fromLessonId);
            });

            $(document).on('change', '.mobile-to-lesson-dropdown', function() {
                const $this = $(this);
                const studentId = $this.data('student-id');
                const toLessonId = $this.val();

                updateToLesson(studentId, toLessonId, $this);
                calculatePages(studentId);
            });

            // Remarks button handler
            $(document).on('click', '.mobile-nouranya-remarks-btn', function() {
                const studentId = $(this).data('student-id');
                const studentName = $(this).data('student-name');

                showRemarksModal(studentId, studentName);
            });
        }

        /**
         * Initialize lazy loading for better performance
         */
        function initializeLazyLoading() {
            // Load lesson data only when needed
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const $section = $(entry.target);
                        const studentId = $section.data('student-id');

                        if (!$section.hasClass('data-loaded')) {
                            loadLessonData(studentId);
                            $section.addClass('data-loaded');
                        }
                    }
                });
            }, {
                rootMargin: '50px'
            });

            $('.mobile-nouranya-section').each(function() {
                observer.observe(this);
            });
        }

        /**
         * Load lesson data via AJAX
         */
        function loadLessonData(studentId) {
            const $section = $(`.mobile-nouranya-section[data-student-id="${studentId}"]`);
            const $fromDropdown = $section.find('.mobile-from-lesson-dropdown');

            if ($fromDropdown.find('option').length > 1) {
                return; // Already loaded
            }

            $section.addClass('mobile-loading');

            $.ajax({
                url: '{{ route("api.student.lessons") }}',
                method: 'GET',
                data: {
                    student_id: studentId,
                    class_id: '{{ $class->id }}',
                    from_date: '{{ $from_date }}'
                },
                success: function(response) {
                    populateLessonDropdowns(studentId, response);
                },
                error: function() {
                    showError('Failed to load lesson data');
                },
                complete: function() {
                    $section.removeClass('mobile-loading');
                }
            });
        }

        /**
         * Populate lesson dropdowns with data
         */
        function populateLessonDropdowns(studentId, data) {
            const $section = $(`.mobile-nouranya-section[data-student-id="${studentId}"]`);
            const $fromDropdown = $section.find('.mobile-from-lesson-dropdown');

            // Clear existing options except first
            $fromDropdown.find('option:not(:first)').remove();

            // Add lesson options
            if (data.lessons && data.lessons.length > 0) {
                data.lessons.forEach(function(lesson) {
                    $fromDropdown.append(
                        `<option value="${lesson.id}">${lesson.name}</option>`
                    );
                });
            }

            // Set current values if available
            if (data.current_from_lesson) {
                $fromDropdown.val(data.current_from_lesson);
                loadToLessons(studentId, data.current_from_lesson);
            }
        }

        /**
         * Load "To" lessons based on "From" lesson selection
         */
        function loadToLessons(studentId, fromLessonId) {
            if (!fromLessonId) return;

            const $section = $(`.mobile-nouranya-section[data-student-id="${studentId}"]`);
            const $toDropdown = $section.find('.mobile-to-lesson-dropdown');

            $.ajax({
                url: '{{ route("api.student.to-lessons") }}',
                method: 'GET',
                data: {
                    student_id: studentId,
                    from_lesson_id: fromLessonId,
                    class_id: '{{ $class->id }}'
                },
                success: function(response) {
                    $toDropdown.find('option:not(:first)').remove();

                    if (response.lessons && response.lessons.length > 0) {
                        response.lessons.forEach(function(lesson) {
                            $toDropdown.append(
                                `<option value="${lesson.id}">${lesson.name}</option>`
                            );
                        });
                    }
                },
                error: function() {
                    showError('Failed to load lesson options');
                }
            });
        }

        /**
         * Update attendance via AJAX
         */
        function updateAttendance(studentId, attendanceId, $element) {
            $element.prop('disabled', true);

            $.ajax({
                url: '{{ route("api.update.attendance") }}',
                method: 'POST',
                data: {
                    student_id: studentId,
                    attendance_id: attendanceId,
                    class_id: '{{ $class->id }}',
                    from_date: '{{ $from_date }}',
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        showSuccess('Attendance updated successfully');

                        // Enable/disable other sections based on attendance
                        const $section = $(`.mobile-nouranya-section[data-student-id="${studentId}"]`);
                        if (attendanceId == '1' || attendanceId == '2') { // Present or Late
                            $section.removeClass('mobile-section-disabled');
                        } else {
                            $section.addClass('mobile-section-disabled');
                        }
                    } else {
                        showError(response.message || 'Failed to update attendance');
                    }
                },
                error: function() {
                    showError('Failed to update attendance');
                },
                complete: function() {
                    $element.prop('disabled', false);
                }
            });
        }

        /**
         * Show remarks modal with Apple-style design
         */
        function showRemarksModal(studentId, studentName) {
            Swal.fire({
                title: `Remarks for ${studentName}`,
                html: `
                    <div class="modal-section-header">Current Remarks</div>
                    <textarea id="mobile-remarks-editor" class="swal2-textarea" placeholder="Enter remarks for this student..."></textarea>

                    <div class="modal-section-header" style="margin-top: 20px;">Quick Templates</div>
                    <div class="template-category">
                        <div class="template-category-title">Performance</div>
                        <span class="template-btn" data-template="Excellent progress today">Excellent progress</span>
                        <span class="template-btn" data-template="Good effort shown">Good effort</span>
                        <span class="template-btn" data-template="Needs more practice">Needs practice</span>
                    </div>

                    <div class="template-category">
                        <div class="template-category-title">Behavior</div>
                        <span class="template-btn" data-template="Very attentive in class">Very attentive</span>
                        <span class="template-btn" data-template="Participated well">Participated well</span>
                        <span class="template-btn" data-template="Needs to focus more">Needs focus</span>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Save Remarks',
                cancelButtonText: 'Cancel',
                customClass: {
                    container: 'swal2-container-fixed',
                    popup: 'swal2-popup-fixed remarks-modal'
                },
                didOpen: function() {
                    // Load existing remarks
                    loadExistingRemarks(studentId);

                    // Template button handlers
                    $('.template-btn').on('click', function() {
                        const template = $(this).data('template');
                        const $textarea = $('#mobile-remarks-editor');
                        const currentText = $textarea.val();

                        if (currentText) {
                            $textarea.val(currentText + '. ' + template);
                        } else {
                            $textarea.val(template);
                        }
                    });
                }
            }).then(function(result) {
                if (result.isConfirmed) {
                    const remarks = $('#mobile-remarks-editor').val();
                    saveRemarks(studentId, remarks);
                }
            });
        }

        /**
         * Load existing remarks for student
         */
        function loadExistingRemarks(studentId) {
            $.ajax({
                url: '{{ route("api.student.remarks") }}',
                method: 'GET',
                data: {
                    student_id: studentId,
                    class_id: '{{ $class->id }}',
                    from_date: '{{ $from_date }}'
                },
                success: function(response) {
                    if (response.remarks) {
                        $('#mobile-remarks-editor').val(response.remarks);
                    }
                }
            });
        }

        /**
         * Save remarks via AJAX
         */
        function saveRemarks(studentId, remarks) {
            Swal.fire({
                title: 'Saving...',
                allowOutsideClick: false,
                didOpen: function() {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '{{ route("api.save.remarks") }}',
                method: 'POST',
                data: {
                    student_id: studentId,
                    remarks: remarks,
                    class_id: '{{ $class->id }}',
                    from_date: '{{ $from_date }}',
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Saved!',
                            text: 'Remarks saved successfully',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        showError(response.message || 'Failed to save remarks');
                    }
                },
                error: function() {
                    showError('Failed to save remarks');
                }
            });
        }

        /**
         * Calculate pages between lessons
         */
        function calculatePages(studentId) {
            const $section = $(`.mobile-nouranya-section[data-student-id="${studentId}"]`);
            const fromLessonId = $section.find('.mobile-from-lesson-dropdown').val();
            const toLessonId = $section.find('.mobile-to-lesson-dropdown').val();

            if (fromLessonId && toLessonId) {
                $.ajax({
                    url: '{{ route("api.calculate.pages") }}',
                    method: 'GET',
                    data: {
                        from_lesson_id: fromLessonId,
                        to_lesson_id: toLessonId
                    },
                    success: function(response) {
                        $section.find('.mobile-pages-count').text(response.pages || 0);
                    }
                });
            }
        }

        /**
         * Utility functions for notifications
         */
        function showSuccess(message) {
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: message,
                timer: 2000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: message,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }

        /**
         * Performance optimization: Cleanup on page unload
         */
        $(window).on('beforeunload', function() {
            // Clear any pending timeouts
            clearTimeout(window.searchTimeout);

            // Remove event listeners
            $(document).off('change', '.mobile-nouranya-attendance-dropdown');
            $(document).off('change', '.mobile-evaluation-dropdown');
            $(document).off('change', '.mobile-from-lesson-dropdown');
            $(document).off('change', '.mobile-to-lesson-dropdown');
            $(document).off('click', '.mobile-nouranya-remarks-btn');
        });
    </script>
@endsection
