<?php

use Illuminate\Support\Facades\Route;
use Modules\Education\Http\Controllers\Api\MobileReportController;

/*
|--------------------------------------------------------------------------
| Mobile Report API Routes
|--------------------------------------------------------------------------
|
| These routes handle AJAX requests from the mobile Nouraniyah reporting
| interface, providing fast, cached responses for lesson data, attendance
| updates, and form submissions.
|
*/

Route::middleware(['auth:employee', 'throttle:60,1'])->prefix('mobile-reports')->group(function () {
    
    // Student lesson data endpoints
    Route::get('/student/lessons', [MobileReportController::class, 'getStudentLessons'])
        ->name('api.student.lessons');
    
    Route::get('/student/to-lessons', [MobileReportController::class, 'getToLessons'])
        ->name('api.student.to-lessons');
    
    Route::get('/calculate-pages', [MobileReportController::class, 'calculatePages'])
        ->name('api.calculate.pages');
    
    // Student data updates
    Route::post('/update/attendance', [MobileReportController::class, 'updateAttendance'])
        ->name('api.update.attendance');
    
    Route::post('/update/evaluation', [MobileReportController::class, 'updateEvaluation'])
        ->name('api.update.evaluation');
    
    Route::post('/update/lesson', [MobileReportController::class, 'updateLesson'])
        ->name('api.update.lesson');
    
    // Remarks functionality
    Route::get('/student/remarks', [MobileReportController::class, 'getStudentRemarks'])
        ->name('api.student.remarks');
    
    Route::post('/save/remarks', [MobileReportController::class, 'saveRemarks'])
        ->name('api.save.remarks');
    
    // Bulk operations for performance
    Route::post('/bulk/update', [MobileReportController::class, 'bulkUpdate'])
        ->name('api.bulk.update');
    
    // Performance monitoring
    Route::post('/performance/log', [MobileReportController::class, 'logPerformance'])
        ->name('api.performance.log');
});
