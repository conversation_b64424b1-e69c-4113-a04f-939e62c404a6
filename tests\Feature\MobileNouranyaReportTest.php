<?php

namespace Tests\Feature;

use App\Classes;
use App\Student;
use App\Employee;
use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\Services\MobilePerformanceService;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

/**
 * Test suite for mobile Nouraniyah reporting functionality.
 * 
 * Purpose: Validates mobile-first daily reporting interface performance,
 * functionality, and user experience across different devices and scenarios.
 * Coverage: Tests form interactions, AJAX submissions, caching, performance
 * optimizations, and mobile-specific UI behaviors.
 */
class MobileNouranyaReportTest extends TestCase
{
    use DatabaseTransactions;

    private Employee $teacher;
    private Classes $class;
    private Student $student;
    private MobilePerformanceService $performanceService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->performanceService = app(MobilePerformanceService::class);
        
        // Create test data
        $this->teacher = Employee::factory()->create();
        $this->class = Classes::factory()->create();
        $this->student = Student::factory()->create();
        
        // Authenticate as teacher
        $this->actingAs($this->teacher, 'employee');
    }

    /**
     * Test mobile report page loads with optimized performance.
     */
    public function test_mobile_report_page_loads_with_performance_optimization(): void
    {
        $startTime = microtime(true);
        
        $response = $this->get(route('reports.create', ['id' => $this->class->id]));
        
        $loadTime = microtime(true) - $startTime;
        
        $response->assertStatus(200);
        $response->assertViewIs('education::classes.reports.nouranya_create_optimized');
        
        // Performance assertion: should load in under 1 second
        $this->assertLessThan(1.0, $loadTime, 'Page should load in under 1 second');
        
        // Check mobile-specific elements are present
        $response->assertSee('mobile-report-container');
        $response->assertSee('mobile-student-accordion');
        $response->assertSee('mobile-search-container');
    }

    /**
     * Test mobile search functionality with debouncing.
     */
    public function test_mobile_search_filters_students_correctly(): void
    {
        $response = $this->get(route('reports.create', ['id' => $this->class->id]));
        
        $response->assertStatus(200);
        
        // Check search input is present
        $response->assertSee('mobileStudentSearch');
        $response->assertSee('Search students by name');
        
        // Verify JavaScript search functionality is loaded
        $response->assertSee('filterStudents');
        $response->assertSee('searchTimeout');
    }

    /**
     * Test student lesson data API endpoint performance.
     */
    public function test_student_lessons_api_returns_cached_data_quickly(): void
    {
        $startTime = microtime(true);
        
        $response = $this->getJson(route('api.student.lessons', [
            'student_id' => $this->student->id,
            'class_id' => $this->class->id,
            'from_date' => now()->format('Y-m-d')
        ]));
        
        $responseTime = microtime(true) - $startTime;
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'lessons',
            'current_from_lesson',
            'current_to_lesson'
        ]);
        
        // Performance assertion: API should respond in under 500ms
        $this->assertLessThan(0.5, $responseTime, 'API should respond in under 500ms');
    }

    /**
     * Test attendance update via AJAX with validation.
     */
    public function test_attendance_update_saves_correctly_via_ajax(): void
    {
        $attendanceOption = AttendanceOption::factory()->create();
        
        $response = $this->postJson(route('api.update.attendance'), [
            'student_id' => $this->student->id,
            'attendance_id' => $attendanceOption->id,
            'class_id' => $this->class->id,
            'from_date' => now()->format('Y-m-d')
        ]);
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Attendance updated successfully'
        ]);
        
        // Verify data was saved to database
        $this->assertDatabaseHas('student_nouranya_reports', [
            'student_id' => $this->student->id,
            'class_id' => $this->class->id,
            'attendance_id' => $attendanceOption->id
        ]);
    }

    /**
     * Test evaluation update handles validation errors gracefully.
     */
    public function test_evaluation_update_handles_validation_errors(): void
    {
        $response = $this->postJson(route('api.update.evaluation'), [
            'student_id' => 'invalid',
            'evaluation_id' => 'invalid',
            'class_id' => $this->class->id,
            'from_date' => now()->format('Y-m-d')
        ]);
        
        $response->assertStatus(400);
        $response->assertJson([
            'error' => 'Invalid parameters'
        ]);
    }

    /**
     * Test remarks functionality saves and retrieves correctly.
     */
    public function test_remarks_save_and_retrieve_functionality(): void
    {
        $remarks = 'Excellent progress in today\'s lesson. Student showed great improvement.';
        
        // Save remarks
        $saveResponse = $this->postJson(route('api.save.remarks'), [
            'student_id' => $this->student->id,
            'remarks' => $remarks,
            'class_id' => $this->class->id,
            'from_date' => now()->format('Y-m-d')
        ]);
        
        $saveResponse->assertStatus(200);
        $saveResponse->assertJson([
            'success' => true,
            'message' => 'Remarks saved successfully'
        ]);
        
        // Retrieve remarks
        $getResponse = $this->getJson(route('api.student.remarks', [
            'student_id' => $this->student->id,
            'class_id' => $this->class->id,
            'from_date' => now()->format('Y-m-d')
        ]));
        
        $getResponse->assertStatus(200);
        $getResponse->assertJson([
            'remarks' => $remarks
        ]);
    }

    /**
     * Test cache performance and hit rates.
     */
    public function test_cache_performance_meets_requirements(): void
    {
        $classId = $this->class->id;
        $fromDate = now()->format('Y-m-d');
        
        // First request should cache data
        $startTime = microtime(true);
        $this->performanceService->preloadClassData($classId, $fromDate);
        $firstLoadTime = microtime(true) - $startTime;
        
        // Second request should use cache
        $startTime = microtime(true);
        $this->performanceService->preloadClassData($classId, $fromDate);
        $cachedLoadTime = microtime(true) - $startTime;
        
        // Cached request should be significantly faster
        $this->assertLessThan($firstLoadTime * 0.1, $cachedLoadTime, 'Cached request should be 10x faster');
        $this->assertLessThan(0.1, $cachedLoadTime, 'Cached request should be under 100ms');
    }

    /**
     * Test mobile UI elements render correctly.
     */
    public function test_mobile_ui_elements_render_correctly(): void
    {
        $response = $this->get(route('reports.create', ['id' => $this->class->id]));
        
        $response->assertStatus(200);
        
        // Check mobile-specific CSS classes
        $response->assertSee('mobile-student-accordion');
        $response->assertSee('mobile-student-header');
        $response->assertSee('mobile-student-content');
        $response->assertSee('mobile-section');
        $response->assertSee('mobile-field-group');
        
        // Check mobile-specific JavaScript functions
        $response->assertSee('initializeMobileSearch');
        $response->assertSee('initializeAccordions');
        $response->assertSee('initializeFormHandlers');
        $response->assertSee('initializeLazyLoading');
    }

    /**
     * Test performance monitoring middleware logs correctly.
     */
    public function test_performance_monitoring_logs_mobile_requests(): void
    {
        // Make request with mobile user agent
        $response = $this->withHeaders([
            'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        ])->get(route('reports.create', ['id' => $this->class->id]));
        
        $response->assertStatus(200);
        
        // Check performance headers are added in debug mode
        if (config('app.debug')) {
            $response->assertHeader('X-Mobile-Performance-Time');
            $response->assertHeader('X-Mobile-Memory-Usage');
        }
    }

    /**
     * Test database query optimization with indexes.
     */
    public function test_database_queries_are_optimized(): void
    {
        // Enable query logging
        \DB::enableQueryLog();
        
        $response = $this->get(route('reports.create', ['id' => $this->class->id]));
        
        $queries = \DB::getQueryLog();
        
        $response->assertStatus(200);
        
        // Should have minimal queries due to optimization
        $this->assertLessThan(10, count($queries), 'Should have fewer than 10 queries');
        
        // Check for efficient joins and indexes usage
        foreach ($queries as $query) {
            $this->assertStringNotContainsString('filesort', strtolower($query['query']), 'Queries should not use filesort');
        }
    }

    /**
     * Test error handling for mobile requests.
     */
    public function test_mobile_error_handling_works_correctly(): void
    {
        // Test with invalid class ID
        $response = $this->get(route('reports.create', ['id' => 99999]));
        
        $response->assertStatus(404);
        
        // Test API error handling
        $apiResponse = $this->getJson(route('api.student.lessons', [
            'student_id' => 99999,
            'class_id' => 99999,
            'from_date' => now()->format('Y-m-d')
        ]));
        
        $apiResponse->assertStatus(400);
        $apiResponse->assertJson([
            'error' => 'Invalid parameters'
        ]);
    }

    /**
     * Test mobile performance optimization command.
     */
    public function test_mobile_optimization_command_runs_successfully(): void
    {
        $this->artisan('mobile:optimize --test')
            ->expectsOutput('🚀 Starting Mobile Performance Optimization...')
            ->expectsOutput('✅ Mobile optimization completed')
            ->assertExitCode(0);
    }
}
