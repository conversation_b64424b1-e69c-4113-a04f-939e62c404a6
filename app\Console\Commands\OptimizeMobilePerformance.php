<?php

namespace App\Console\Commands;

use App\Services\MobilePerformanceService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * Command to optimize mobile performance for Nouraniyah reporting.
 * 
 * Purpose: Automates performance optimization tasks including cache warming,
 * database optimization, and system configuration for mobile users.
 * Side effects: Clears caches, optimizes database tables, updates configurations,
 * and runs performance tests.
 */
class OptimizeMobilePerformance extends Command
{
    protected $signature = 'mobile:optimize 
                           {--clear-cache : Clear all mobile caches}
                           {--warm-cache : Warm up critical caches}
                           {--optimize-db : Optimize database for mobile queries}
                           {--test : Run performance tests}
                           {--all : Run all optimizations}';

    protected $description = 'Optimize mobile performance for Nouraniyah reporting';

    private MobilePerformanceService $performanceService;

    public function __construct(MobilePerformanceService $performanceService)
    {
        parent::__construct();
        $this->performanceService = $performanceService;
    }

    public function handle(): int
    {
        $this->info('🚀 Starting Mobile Performance Optimization...');
        
        $startTime = microtime(true);
        
        if ($this->option('all')) {
            $this->runAllOptimizations();
        } else {
            if ($this->option('clear-cache')) {
                $this->clearMobileCaches();
            }
            
            if ($this->option('warm-cache')) {
                $this->warmCaches();
            }
            
            if ($this->option('optimize-db')) {
                $this->optimizeDatabase();
            }
            
            if ($this->option('test')) {
                $this->runPerformanceTests();
            }
        }
        
        $executionTime = microtime(true) - $startTime;
        
        $this->info("✅ Mobile optimization completed in " . round($executionTime, 2) . " seconds");
        
        return 0;
    }

    private function runAllOptimizations(): void
    {
        $this->clearMobileCaches();
        $this->optimizeDatabase();
        $this->warmCaches();
        $this->runPerformanceTests();
    }

    private function clearMobileCaches(): void
    {
        $this->info('🧹 Clearing mobile caches...');
        
        $cleared = $this->performanceService->clearMobileCaches();
        
        // Clear Laravel caches
        Artisan::call('cache:clear');
        Artisan::call('view:clear');
        Artisan::call('route:clear');
        
        $this->info("   Cleared {$cleared} mobile cache keys");
        $this->info('   Cleared Laravel application caches');
    }

    private function optimizeDatabase(): void
    {
        $this->info('🗄️  Optimizing database for mobile queries...');
        
        // Run migrations for mobile indexes
        Artisan::call('migrate', ['--path' => 'database/migrations/2025_08_28_000001_add_mobile_performance_indexes.php']);
        
        // Optimize critical tables
        $tables = [
            'students',
            'student_nouranya_plans', 
            'student_nouranya_reports',
            'student_joint_classes',
            'program_level_lessons',
            'student_program_levels'
        ];
        
        foreach ($tables as $table) {
            DB::statement("OPTIMIZE TABLE {$table}");
            $this->info("   Optimized table: {$table}");
        }
        
        // Update table statistics
        foreach ($tables as $table) {
            DB::statement("ANALYZE TABLE {$table}");
        }
        
        $this->info('   Updated table statistics for query optimization');
    }

    private function warmCaches(): void
    {
        $this->info('🔥 Warming up critical caches...');
        
        // Get active classes with Nouraniyah programs
        $classes = DB::table('classes as c')
            ->join('class_programs as cp', 'c.id', '=', 'cp.class_id')
            ->join('programs as p', 'cp.program_id', '=', 'p.id')
            ->where('p.title', 'like', '%nuraniyah%')
            ->where('c.status', 'active')
            ->select('c.id')
            ->limit(10) // Limit to prevent timeout
            ->get();

        $warmedCount = 0;
        foreach ($classes as $class) {
            // Warm cache for today's date
            $today = now()->format('Y-m-d');
            $this->performanceService->preloadClassData($class->id, $today);
            $warmedCount++;
            
            $this->info("   Warmed cache for class {$class->id}");
        }
        
        // Warm global caches
        Cache::remember('attendance_options_mobile', 3600, function() {
            return DB::table('attendance_options')->get();
        });
        
        $this->info("   Warmed {$warmedCount} class caches and global options");
    }

    private function runPerformanceTests(): void
    {
        $this->info('🧪 Running performance tests...');
        
        // Test database query performance
        $this->testDatabasePerformance();
        
        // Test cache performance
        $this->testCachePerformance();
        
        // Display current performance stats
        $this->displayPerformanceStats();
    }

    private function testDatabasePerformance(): void
    {
        $this->info('   Testing database query performance...');
        
        // Test critical mobile queries
        $queries = [
            'Student lookup' => function() {
                return DB::table('students as s')
                    ->join('student_joint_classes as sjc', 's.id', '=', 'sjc.student_id')
                    ->where('sjc.class_id', 1)
                    ->where('s.status', 'active')
                    ->count();
            },
            'Nouranya plans' => function() {
                return DB::table('student_nouranya_plans')
                    ->where('class_id', 1)
                    ->where('status', 'active')
                    ->count();
            },
            'Daily reports' => function() {
                return DB::table('student_nouranya_reports')
                    ->where('class_id', 1)
                    ->whereDate('created_at', now())
                    ->count();
            }
        ];

        foreach ($queries as $name => $query) {
            $startTime = microtime(true);
            $result = $query();
            $executionTime = (microtime(true) - $startTime) * 1000;
            
            $status = $executionTime < 100 ? '✅' : ($executionTime < 500 ? '⚠️' : '❌');
            $this->info("     {$status} {$name}: {$executionTime}ms ({$result} records)");
        }
    }

    private function testCachePerformance(): void
    {
        $this->info('   Testing cache performance...');
        
        // Test cache write performance
        $startTime = microtime(true);
        Cache::put('mobile_test_key', ['test' => 'data'], 60);
        $writeTime = (microtime(true) - $startTime) * 1000;
        
        // Test cache read performance
        $startTime = microtime(true);
        $data = Cache::get('mobile_test_key');
        $readTime = (microtime(true) - $startTime) * 1000;
        
        // Clean up
        Cache::forget('mobile_test_key');
        
        $writeStatus = $writeTime < 10 ? '✅' : ($writeTime < 50 ? '⚠️' : '❌');
        $readStatus = $readTime < 5 ? '✅' : ($readTime < 25 ? '⚠️' : '❌');
        
        $this->info("     {$writeStatus} Cache write: {$writeTime}ms");
        $this->info("     {$readStatus} Cache read: {$readTime}ms");
    }

    private function displayPerformanceStats(): void
    {
        $this->info('📊 Current Performance Statistics:');
        
        $stats = $this->performanceService->getPerformanceStats();
        
        $this->info("   Total requests today: {$stats['total_requests']}");
        $this->info("   Average load time: {$stats['average_load_time']}s");
        $this->info("   Cache hit rate: {$stats['cache_hit_rate']}%");
        $this->info("   Active DB connections: {$stats['active_connections']}");
        
        // Performance recommendations
        if ($stats['average_load_time'] > 1.0) {
            $this->warn('⚠️  Average load time is high. Consider additional optimizations.');
        }
        
        if ($stats['cache_hit_rate'] < 80) {
            $this->warn('⚠️  Cache hit rate is low. Consider warming more caches.');
        }
        
        if ($stats['average_load_time'] < 0.5 && $stats['cache_hit_rate'] > 90) {
            $this->info('🎉 Excellent performance! Mobile users should have a great experience.');
        }
    }
}
