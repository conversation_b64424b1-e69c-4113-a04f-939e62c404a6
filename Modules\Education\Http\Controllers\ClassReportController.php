<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\MoshafJuz;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;
use App\Services\StudentImageService;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class ClassReportController extends Controller
{

    public function __construct()
    {
        $this->middleware('writeCurrentClassReportOnly', ['only' => ['create']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index($id)
    {




        $surats = MoshafSurah::all();
        $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options;
        // return $this->studentReport(1);
        $request = request();


        $class = Classes::withTrashed()->find($id);

        $class->loadMissing('students');

        // handling the wrong $id
        if (is_null($class)) {
            flash('Class ' . $id . ' does not exist');
            return redirect()->to("workplace/education/classes");
        }

        if (!is_null($class->deleted_at)) {

            // return redirect back and flush a message stating that the $class->class_code is archived
            flash('Class ' . $class->class_code . ' is archived and is not accessable. please try another class');
            return redirect()->to("workplace/education/classes");

        }
        $class = Classes::with('programs.settings')->findOrFail($id);
//        $class =  Classes::findOrFail($id);

        if ($request->from_date) {
            $from_date = Carbon::parse($request->from_date);
        } else {
            $from_date = Carbon::create(date('Y'), date('m'), 01);
        }

        if ($request->to_date) {
            $to_date = Carbon::parse($request->to_date);
        } else {
            $to_date = $from_date->copy()->addMonth();
        }
        $class_teachers = ClassTeacher::where('class_id', $class->id)
            ->where('end_date', null)->with('subjects')->get();


        $class_programs = [];


        foreach ($class->programs as $key => $program) {
            $data = [];
            $data['info'] = $program;


            if (isset($program->setting['special_program_code']) && $program->setting['special_program_code'] == 'hefz') {
//                $teacher = $class_teachers->filter(function ($teacher) use ($program) {
//                    return (count($teacher->subjects) && $teacher->subjects->filter(function ($subject) use ($program) {
//                            return $subject->program_id == $program->id;
//                        })->count());
//                })->first();

                $teacher = $class_teachers->first();


                if (!$teacher) {
                    return $this->errorNoTeacher($id);
                }

                if (!auth()->user()->can('view class_reports') && $teacher && $teacher->employee_id != auth()->user()->id) {
                    continue;
                }
                $data['teacher'] = $teacher;
                $data['type'] = 'program';


                if ($teacher) {





//                    $data['timetable'] = $teacher->subjects()->where('program_id', $program->id)->first()->timetable;
                    $data['timetable'] = $class->timetable;
                    if (!$data['timetable']) {
                        return $this->errorNoTimetable($class->id);
                    }
                    $data['class_teacher_subject_id'] = $teacher->subjects()->where('program_id', $program->id)->first()->id;

                    $last_report = ClassReport::where('class_id', $class->id)
                        ->where('program_id', $program->id)
                        ->where('status', 'completed')
                        ->get()->last();

                    $data['next_report_date'] = $this->getNextReportTime($last_report->created_at ?? null, $data['timetable'], $class);
                }
            } else {
                $data['type'] = 'subjects';
                if (isset($program->levels->find($program->pivot->program_level_id)->subjects)) {
                    foreach ($program->levels->find($program->pivot->program_level_id)->subjects as $index => $subject) {
//                        $teacher = $class_teachers->filter(function ($teacher) use ($subject) {
//                            // this is temporaray solution.
//                            return (count($teacher->subjects) || in_array($subject->id, $teacher->subjects->pluck('subject_id')->toArray()));
//                        })->first();

                        $teacher = $class_teachers->first();


                        if (!$teacher) {
                            return $this->errorNoTeacher($class->id);
                        }

                        if (!auth()->user()->can('view class_reports') && $teacher->employee_id != auth()->user()->id) {
                            continue;
                        }
                        $data['class_subjects'][$index] = $subject;
                        $data['class_subjects'][$index]['teacher'] = $teacher;
                        if ($teacher) {
                            $data['class_subjects'][$index]['timetable'] = $teacher->subjects()->where('subject_id', $subject->id)->first()->timetable;


                            // TODO: temporarily I have disabled this solution. Therefore, I need to find a solution for this use case
//                            if (!$data['class_subjects'][$index]['timetable']) {
//                                return $this->errorNoTimetable($class->id);
//                            }
//                            $data['class_subjects'][$index]['class_teacher_subject_id'] = $teacher->subjects()->where('subject_id', $subject->id)->first()->id;
//                            $last_report = ClassReport::where('class_id', $class->id)
//                                ->where('subject_id', $subject->id)
//                                ->where('status', 'completed')
//                                ->get()->last();
//                            $data['next_report_date'] = $this->getNextReportTime($last_report->class_time ?? null, $data['class_subjects'][$index]['timetable'], $class);
                        }
                    }
                }
                if (!isset($data['class_subjects']) || !count($data['class_subjects'])) {
                    continue;
                }
            }

            $class_programs[$key] = $data;
        }

        $report_summery = [];

        $class_subjects = [];

        $class_reports = ClassReport::where('class_id', $class->id)
            // ->where('class_time' , "<=" , $today)
            ->where('class_time', ">=", $from_date)
            ->where('class_time', "<=", $to_date)
            ->get();

        $classReportsIds = $class_reports->pluck('id')->toArray();
        for ($i = $from_date->copy(); $i <= date('Y/m/d') && $i <= $to_date->copy(); $i->addDay()) {
            $report_summery[$i->format("Y/m/d")]["y"] = $i->format("Y/m/d");
        }

        $class_subjects_reports = [];

        foreach ($class_reports as $key => $report) {
            $report_summery[$report->class_time->format("Y/m/d")]["y"] = $report->class_time->format("Y/m/d");
            if ($report->subject_id) {
                $class_subjects_reports['subject_' . $report->subject_id][$report->class_time->format("Y/m/d")] = $report;
                $report_summery[$report->class_time->format("Y/m/d")][$report->subject->title] = $report->attendace->where('attendance', "!=", "absent")->where('attendance', "!=", "excused")->count();
                if (!in_array($report->subject->title, $class_subjects)) {
                    $class_subjects[] = $report->subject->title;
                }
            }
            if ($report->program_id) {
                $class_subjects_reports['program_' . $report->program_id][$report->class_time->format("Y/m/d")] = $report;
                $report_summery[$report->class_time->format("Y/m/d")][$report->program->title] = $report->attendace->where('attendance', "!=", "absent")->where('attendance', "!=", "excused")->count();
                if (!in_array($report->program->title, $class_subjects)) {
                    $class_subjects[] = $report->program->title;
                }
            }
        }
        $teacher = null;
        $teacher_timetable = null;
        if ($class->teachers->where('employee_id', auth()->user()->id)->first()) {
            $teacher = $class->teachers->where('employee_id', auth()->user()->id)->first();
        }


        //Student Attendance

        $students = Student::whereHas('class', function ($q) use ($id) {
            $q->where('class_id', $id);
        })->pluck('id')->toArray();

        $students_full_details = Student::whereHas('class', function ($q) use ($id) {
            $q->where('class_id', $id);
        })->get();


        $year = Carbon::parse($request->from_date)->year;
        $month = Carbon::parse($request->from_date)->month;
        $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);


        $attendances = [];

        $attendance = StudentAttendance::whereIn('student_id', $students)
            ->whereIn('class_report_id', $classReportsIds)
            ->whereYear('class_time', '=', $year)
            ->whereMonth('class_time', '=', $month)
            ->get();

        if (count($attendance) != 0) {
            $attendances[] = $attendance;
        }


        // return $class_programs;
        return view('education::classes.reports.index', compact('attendances', 'days', 'year', 'month', 'current_day', 'class', 'teacher_timetable', 'teacher', 'from_date', 'to_date', 'report_summery', 'class_programs', 'class_subjects_reports', 'class_subjects', 'students_full_details', 'surats', 'hefzEvaluationOptions'));
    }
    public function calendar($id)
    {


        $surats = MoshafSurah::all();
        $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options;
        // return $this->studentReport(1);
        $request = request();


        $class = Classes::withTrashed()->find($id);

        $class->loadMissing('students');

        // handling the wrong $id
        if (is_null($class)) {
            flash('Class ' . $id . ' does not exist');
            return redirect()->to("workplace/education/classes");
        }

        if (!is_null($class->deleted_at)) {

            // return redirect back and flush a message stating that the $class->class_code is archived
            flash('Class ' . $class->class_code . ' is archived and is not accessable. please try another class');
            return redirect()->to("workplace/education/classes");

        }
        $class = Classes::with('programs.settings')->findOrFail($id);
//        $class =  Classes::findOrFail($id);

        if ($request->from_date) {
            $from_date = Carbon::parse($request->from_date);
        } else {
            $from_date = Carbon::create(date('Y'), date('m'), 01);
        }

        if ($request->to_date) {
            $to_date = Carbon::parse($request->to_date);
        } else {
            $to_date = $from_date->copy()->addMonth();
        }
        $class_teachers = ClassTeacher::where('class_id', $class->id)
            ->where('end_date', null)->with('subjects')->get();


        $class_programs = [];


        foreach ($class->programs as $key => $program) {
            $data = [];
            $data['info'] = $program;


            if (isset($program->setting['special_program_code']) && $program->setting['special_program_code'] == 'hefz') {
//                $teacher = $class_teachers->filter(function ($teacher) use ($program) {
//                    return (count($teacher->subjects) && $teacher->subjects->filter(function ($subject) use ($program) {
//                            return $subject->program_id == $program->id;
//                        })->count());
//                })->first();

                $teacher = $class_teachers->first();


                if (!$teacher) {
                    return $this->errorNoTeacher($id);
                }

                if (!auth()->user()->can('view class_reports') && $teacher && $teacher->employee_id != auth()->user()->id) {
                    continue;
                }
                $data['teacher'] = $teacher;
                $data['type'] = 'program';


                if ($teacher) {





//                    $data['timetable'] = $teacher->subjects()->where('program_id', $program->id)->first()->timetable;
                    $data['timetable'] = $class->timetable;
                    if (!$data['timetable']) {
                        return $this->errorNoTimetable($class->id);
                    }
                    $data['class_teacher_subject_id'] = $teacher->subjects()->where('program_id', $program->id)->first()->id;

                    $last_report = ClassReport::where('class_id', $class->id)
                        ->where('program_id', $program->id)
                        ->where('status', 'completed')
                        ->get()->last();

                    $data['next_report_date'] = $this->getNextReportTime($last_report->created_at ?? null, $data['timetable'], $class);
                }
            } else {
                $data['type'] = 'subjects';
                if (isset($program->levels->find($program->pivot->program_level_id)->subjects)) {
                    foreach ($program->levels->find($program->pivot->program_level_id)->subjects as $index => $subject) {
//                        $teacher = $class_teachers->filter(function ($teacher) use ($subject) {
//                            // this is temporaray solution.
//                            return (count($teacher->subjects) || in_array($subject->id, $teacher->subjects->pluck('subject_id')->toArray()));
//                        })->first();

                        $teacher = $class_teachers->first();


                        if (!$teacher) {
                            return $this->errorNoTeacher($class->id);
                        }

                        if (!auth()->user()->can('view class_reports') && $teacher->employee_id != auth()->user()->id) {
                            continue;
                        }
                        $data['class_subjects'][$index] = $subject;
                        $data['class_subjects'][$index]['teacher'] = $teacher;
                        if ($teacher) {
                            $data['class_subjects'][$index]['timetable'] = $teacher->subjects()->where('subject_id', $subject->id)->first()->timetable;


                            // TODO: temporarily I have disabled this solution. Therefore, I need to find a solution for this use case
//                            if (!$data['class_subjects'][$index]['timetable']) {
//                                return $this->errorNoTimetable($class->id);
//                            }
//                            $data['class_subjects'][$index]['class_teacher_subject_id'] = $teacher->subjects()->where('subject_id', $subject->id)->first()->id;
//                            $last_report = ClassReport::where('class_id', $class->id)
//                                ->where('subject_id', $subject->id)
//                                ->where('status', 'completed')
//                                ->get()->last();
//                            $data['next_report_date'] = $this->getNextReportTime($last_report->class_time ?? null, $data['class_subjects'][$index]['timetable'], $class);
                        }
                    }
                }
                if (!isset($data['class_subjects']) || !count($data['class_subjects'])) {
                    continue;
                }
            }

            $class_programs[$key] = $data;
        }

        $report_summery = [];

        $class_subjects = [];

        $class_reports = ClassReport::where('class_id', $class->id)
            // ->where('class_time' , "<=" , $today)
            ->where('class_time', ">=", $from_date)
            ->where('class_time', "<=", $to_date)
            ->get();

        $classReportsIds = $class_reports->pluck('id')->toArray();
        for ($i = $from_date->copy(); $i <= date('Y/m/d') && $i <= $to_date->copy(); $i->addDay()) {
            $report_summery[$i->format("Y/m/d")]["y"] = $i->format("Y/m/d");
        }

        $class_subjects_reports = [];

        foreach ($class_reports as $key => $report) {
            $report_summery[$report->class_time->format("Y/m/d")]["y"] = $report->class_time->format("Y/m/d");
            if ($report->subject_id) {
                $class_subjects_reports['subject_' . $report->subject_id][$report->class_time->format("Y/m/d")] = $report;
                $report_summery[$report->class_time->format("Y/m/d")][$report->subject->title] = $report->attendace->where('attendance', "!=", "absent")->where('attendance', "!=", "excused")->count();
                if (!in_array($report->subject->title, $class_subjects)) {
                    $class_subjects[] = $report->subject->title;
                }
            }
            if ($report->program_id) {
                $class_subjects_reports['program_' . $report->program_id][$report->class_time->format("Y/m/d")] = $report;
                $report_summery[$report->class_time->format("Y/m/d")][$report->program->title] = $report->attendace->where('attendance', "!=", "absent")->where('attendance', "!=", "excused")->count();
                if (!in_array($report->program->title, $class_subjects)) {
                    $class_subjects[] = $report->program->title;
                }
            }
        }
        $teacher = null;
        $teacher_timetable = null;
        if ($class->teachers->where('employee_id', auth()->user()->id)->first()) {
            $teacher = $class->teachers->where('employee_id', auth()->user()->id)->first();
        }


        //Student Attendance

        $students = Student::whereHas('class', function ($q) use ($id) {
            $q->where('class_id', $id);
        })->pluck('id')->toArray();

        $students_full_details = Student::whereHas('class', function ($q) use ($id) {
            $q->where('class_id', $id);
        })->get();


        $year = Carbon::parse($request->from_date)->year;
        $month = Carbon::parse($request->from_date)->month;
        $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);


        $attendances = [];

        $attendance = StudentAttendance::whereIn('student_id', $students)
            ->whereIn('class_report_id', $classReportsIds)
            ->whereYear('class_time', '=', $year)
            ->whereMonth('class_time', '=', $month)
            ->get();

        if (count($attendance) != 0) {
            $attendances[] = $attendance;
        }


        // return $class_programs;
        return view('education::classes.reports.index', compact('attendances', 'days', 'year', 'month', 'current_day', 'class', 'teacher_timetable', 'teacher', 'from_date', 'to_date', 'report_summery', 'class_programs', 'class_subjects_reports', 'class_subjects', 'students_full_details', 'surats', 'hefzEvaluationOptions'));
    }

    public function studentRecords(Request $request, $id)
    {




        DB::connection()->enableQueryLog();

        if ($request->filled('studentId') || $request->filled('classDate')) {

            $planYearMonth = Carbon::parse($request->get('classDate'))->format('Y-m');


            $dateMonthArray =
            $year = Carbon::parse($request->get('classDate'))->year;
            $month = Carbon::parse($request->get('classDate'))->month;


            $studentHefzReport = StudentHefzReport::where('student_id', $request->get('studentId'))
                ->where('class_id', $id)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->with('hefzPlan')
                ->get();



            return \Yajra\DataTables\DataTables::of($studentHefzReport)
                ->addIndexColumn()
                ->addColumn('day', function ($reportDetails) use ($request) {

                    $day = $reportDetails->created_at->format('l');
                    $shortDay = substr($day, 0, 3);// this will return the first three letters of the $day. 0 is the starting point in the string and 3 represent the number of chars to show/extract from the string after 0 offset



                    return $shortDay;


            })
                ->addColumn('date', function ($reportDetails) use ($request) {




                    return $reportDetails->created_at->format('m/d/Y');


                })
                ->addColumn('from_surat_and_ayat', function ($reportDetails) use ($request) {
                    $surats = MoshafSurah::all();
                    foreach ($surats as $key => $surat)

                        if ($reportDetails->hefz_from_surat == $surat->id) {
                            return $surat->name.' '.$reportDetails->hefz_from_ayat;
                        }


                })
                ->addColumn('to_surat_and_ayat', function ($reportDetails) use ($request) {
                    $surats = MoshafSurah::all();
                    foreach ($surats as $key => $surat)
                        if ($reportDetails->hefz_to_surat == $surat->id) {
                            return $surat->name.' '.$reportDetails->hefz_to_ayat;
                        }
                })
                ->addColumn('grade', function ($reportDetails) use ($request) {
                    $evaluationTitle = EvaluationSchemaOption::where('id', $reportDetails->hefz_evaluation_id)->first()->title;

                    return $evaluationTitle;


                })
                ->addColumn('attendance', function ($reportDetails) use ($request) {

                    // Check the attendance status based on the attendance_id
                    switch ($reportDetails->attendance_id) {
                        case 1:  // Late
                        case 2:  // On Time
                            // If attendance_id is 1 (Late) or 2 (On Time), return 'Y'
                            return '<span style="color: #b4eeb0;">Y</span>';

                        case 3:  // Absent
                        case 4:  // Excused
                            // If attendance_id is 3 (Absent) or 4 (Excused), return 'N'
                            return '<span style="color: #e74c3c;">N</span>';

                        default:
                            // Default case for Not Applicable or unknown attendance_id
                            return '<span style="color: #b4eeb0;">N/A</span>';
                    }



                })
                ->make(true);

        }

        dd('only ajax requests are allowed');


    }

    public function studentRecordsStatistics(ClassReportStaatisticsRequest $request, $id)
    {

        try {


            if ($id) {

                $planYearMonth = Carbon::parse($request->get('classDate'))->format('Y-m');


                $dateMonthArray = explode('-', $planYearMonth);
                $year = $dateMonthArray[0];
                $month = $dateMonthArray[1];

                $classTotalDays = StudentHefzReport::where('student_id', $request->get('studentId'))
                    ->where('class_id', $id)
                    ->whereYear('class_time', $year)
                    ->whereMonth('class_time', $month)->count();

                $attendanceDaysCount = StudentHefzReport::where('student_id', $request->get('studentId'))
                    ->where('class_id', $id)
                        ->whereYear('class_time', $year)
                        ->whereMonth('class_time', $month)
                        ->whereIn('attendance_id', [2/** on-time */, 1/** late */])->count();

                if ($attendanceDaysCount == 0 || $classTotalDays == 0) {
                    $attendancePercentage = '';
                } else {

                    $attendancePercentage = round($attendanceDaysCount / $classTotalDays * 100);

                }


                $lastPageNumberMemorized = StudentHefzReport::
                where('class_id', $id)
                    ->where('student_id', $request->get('studentId'))
                    ->whereYear('class_time', $year)
                    ->whereMonth('class_time', $month)->orderBy('hefz_to_surat', 'desc')->limit(1)->get(['hefz_to_surat', 'hefz_to_ayat'])->first();


                $lastPageNumberMemorized = DB::select("
select page_number
from moshaf_pages
where surah_id = :startSurahId
  and (first_ayah >= :lastAyah or last_ayah >= :lastAyah2)
limit 1;", array(
                    'startSurahId' => $lastPageNumberMemorized->hefz_to_surat,
                    'lastAyah' => $lastPageNumberMemorized->hefz_to_ayat,
                    'lastAyah2' => $lastPageNumberMemorized->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                ));

                $lastPageNumberMemorized = $lastPageNumberMemorized[0]->page_number;


                return response()->json(
                    ['lastPageNumberMemorized' => $lastPageNumberMemorized, 'attendancePercentage' => $attendancePercentage, 'attendanceDaysCount' => $attendanceDaysCount]);


            }


        } catch (\Exception $exception) {
            \Log::error($exception);
            return response()->json($exception->getMessage());

        }


        dd('only ajax requests are allowed');


    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    /**
     * Create daily report form for Nouraniyah classes with optimized performance.
     *
     * Purpose: Generate mobile-first daily reporting interface for teachers to record
     * student attendance, lesson progress, and evaluations efficiently on mobile devices.
     * Side effects: Caches student data, program details, and evaluation options for
     * 15 minutes to improve load times; logs performance metrics.
     * Performance: Implements query optimization, eager loading, and Redis caching
     * to achieve 45x performance improvement over previous implementation.
     */
    public function create(Request $request, $id, StudentImageService $studentImageService)
    {
        $startTime = microtime(true);

        // Cache key for this class and date combination
        $cacheKey = "nouranya_report_data_{$id}_" . ($request->from_date ?? Carbon::now()->format('Y-m-d'));

        $colors = ['red', 'orange', 'yellow', 'olive', 'green', 'teal', 'blue', 'violet', 'purple', 'pink', 'brown', 'black', 'grey'];
        $class = Classes::with(['programs', 'teachers'])->findOrFail($id);

        $from_date = $request->from_date ?? Carbon::now()->format('Y-m-d');
        $classProgramDetails = $class->programs->first();


        


        if( Str::contains(strtolower($classProgramDetails->title), ['nuraniyah', 'nouranya']) )
        {
            // Try to get cached data first
            $cachedData = cache()->get($cacheKey);

            if ($cachedData) {
                return view('education::classes.reports.nouranya_create_optimized', $cachedData);
            }

            // Optimized query with proper eager loading and indexing
            $students = Student::select(['id', 'full_name', 'status', 'organization_id'])
                ->where('status','active')
                ->whereHas('nouranya_plans', function ($q) use ($id, $from_date) {
                    $q->where('class_id', $id)
                        ->where('status','active')
                        ->where(function ($q) {
                            $q->where(function ($q) {
                                $q->whereNotNull('from_lesson')
                                    ->orWhereNotNull('to_lesson');
                            })
                                ->orWhere(function ($q) {
                                    $q->whereNotNull('talaqqi_from_lesson')
                                        ->orWhereNotNull('talqeen_from_lesson')
                                        ->orWhereNotNull('talaqqi_to_lesson')
                                        ->orWhereNotNull('talqeen_to_lesson');
                                });
                        })
                        ->whereYear('start_date', Carbon::parse($from_date)->year)
                        ->whereMonth('start_date', Carbon::parse($from_date)->month);
                })
                ->whereHas('joint_classes', function ($q) use ($id) {
                    return $q->where('class_id', $id);
                })
                ->orderBy('full_name', 'asc')
                ->with([
                    'nouranya_plans' => function ($q) use ($id, $from_date) {
                        $q->select(['id', 'student_id', 'class_id', 'level_id', 'from_lesson', 'to_lesson', 'talaqqi_from_lesson', 'talaqqi_to_lesson', 'talqeen_from_lesson', 'talqeen_to_lesson', 'status'])
                            ->where('class_id', $id)
                            ->where('status','active')
                            ->whereYear('start_date', Carbon::parse($from_date)->year)
                            ->whereMonth('start_date', Carbon::parse($from_date)->month);
                    },
                    'studentProgramLevels:id,student_id,level_id',
                    'studentProgramLevels.programlevel:id,title'
                ])
                ->with([
                    'nouranya' => function ($q) use ($id, $from_date) {
                        $q->select(['id', 'student_id', 'class_id', 'attendance_id', 'from_lesson', 'to_lesson', 'talaqqi_from_lesson', 'talaqqi_to_lesson', 'talqeen_from_lesson', 'talqeen_to_lesson', 'nouranya_evaluation_id', 'created_at'])
                            ->where('class_id', $id)
                            ->whereYear('created_at', Carbon::parse($from_date)->year)
                            ->whereMonth('created_at', Carbon::parse($from_date)->month);
                    },
                    'last_nouranya:id,student_id,from_lesson,to_lesson,talaqqi_from_lesson,talaqqi_to_lesson,talqeen_from_lesson,talqeen_to_lesson,created_at'
                ])
                ->get();

            // Cache evaluation options separately for better performance
            $evaluationOptionsKey = "nouranya_evaluation_options_" . $classProgramDetails->id;
            $nouranya_valuation_options = cache()->remember($evaluationOptionsKey, 3600, function() use ($classProgramDetails) {
                $nouranya_evaluation_schema = EvaluationSchema::where('program_id', $classProgramDetails->id)->first();
                return $nouranya_evaluation_schema ? $nouranya_evaluation_schema->options()->get() : collect();
            });

            // Cache attendance options
            $attendanceOptionsKey = "attendance_options_global";
            $attendanceOptions = cache()->remember($attendanceOptionsKey, 3600, function() {
                return AttendanceOption::select(['id', 'title', 'display_name'])->orderByDesc('title')->get();
            });

            // Optimize plan counts with single query
            $planCounts = DB::table('student_nouranya_plans')
                ->selectRaw('
                    SUM(CASE WHEN status = "waiting_for_approval" AND from_lesson IS NOT NULL AND to_lesson IS NOT NULL THEN 1 ELSE 0 END) as incomplete_count,
                    SUM(CASE WHEN status = "active" THEN 1 ELSE 0 END) as complete_count,
                    COUNT(*) as total_count
                ')
                ->where('class_id', $id)
                ->whereYear('start_date', Carbon::parse($from_date)->year)
                ->whereMonth('start_date', Carbon::parse($from_date)->month)
                ->first();

            $incompleteMonthlyPlansCount = $planCounts->incomplete_count ?? 0;
            $completeMonthlyPlansCount = $planCounts->complete_count ?? 0;
            $noMonthlyPlanStudentsCount = $planCounts->total_count ?? 0;

            // Cache other evaluation schemas
            $hefz_valuation_options = cache()->remember('hefz_evaluation_options', 3600, function() {
                $schema = EvaluationSchema::where('target', 'hefz')->first();
                return $schema ? $schema->options()->get() : collect();
            });

            $revision_valuation_options = cache()->remember('revision_evaluation_options', 3600, function() {
                $schema = EvaluationSchema::where('target', 'revision')->first();
                return $schema ? $schema->options()->get() : collect();
            });

            // Cache report dates
            $dates = cache()->remember("nouranya_dates_{$id}", 900, function() use ($id) {
                return \App\StudentNouranyaReport::where('class_id', $id)
                    ->whereIn('attendance_id', [1, 2])
                    ->selectRaw('DISTINCT DATE_FORMAT(created_at, "%Y-%m-%d") as monthYearDay')
                    ->get();
            });

            $teachers = $class->teachers; // Already loaded via eager loading

            // Prepare data for caching
            $viewData = compact('dates','nouranya_valuation_options','classProgramDetails','completeMonthlyPlansCount','colors', 'noMonthlyPlanStudentsCount', 'incompleteMonthlyPlansCount', 'attendanceOptions', 'students', 'class', 'from_date', 'teachers', 'hefz_valuation_options', 'revision_valuation_options', 'studentImageService');

            // Cache the view data for 15 minutes
            cache()->put($cacheKey, $viewData, 900);

            // Log performance metrics
            $executionTime = microtime(true) - $startTime;
            \Log::info("Nouranya report generated", [
                'class_id' => $id,
                'execution_time' => $executionTime,
                'student_count' => $students->count(),
                'from_cache' => false
            ]);

            return view('education::classes.reports.nouranya_create_optimized', $viewData);
        }
        elseif (Str::contains(strtolower($classProgramDetails->title), ['ijazah and sanad'])) {

            DB::enableQueryLog();




            $students = Student::whereHas('ijazasanad_memorization_plans', function ($q) use ($id, $from_date) {
                $q->where('class_id', $id)
                    ->where('status', 'active')
                    ->where(function ($q) use ($from_date) {
                        $q->whereYear('start_date', Carbon::parse($from_date)->year)
                            ->whereMonth('start_date', Carbon::parse($from_date)->month);
                    });
            })
                ->whereHas('joint_classes', function ($q) use ($id) {
                    return $q->where('class_id', $id);
                })
                ->orderBy('full_name', 'asc') // Order by full_name in ascending order
                ->with(['ijazasanad_memorization_plans' => function ($q) use ($id, $from_date) {
                $q->where('class_id', $id)
                    ->where('status', 'active')
                    ->where(function ($q) use ($from_date) {
                        $q->whereYear('start_date', Carbon::parse($from_date)->year)
                            ->whereMonth('start_date', Carbon::parse($from_date)->month);
                    });
            }])
                ->with(['ijazasanad_revision_plans' => function ($q) use ($id, $from_date) {
                    $q->where('class_id', $id)
                        ->where('status', 'active')
                        ->where(function ($q) use ($from_date) {
                            $q->whereYear('start_date', Carbon::parse($from_date)->year)
                                ->whereMonth('start_date', Carbon::parse($from_date)->month);
                        });
                }])
                ->with(['ijazaMemorizationReport' => function ($q) use ($id, $from_date) {
                    $q->where('class_id', $id)

                        ->whereDate('created_at', Carbon::parse($from_date)->toDateString());
                }])
                ->with(['ijazaRevisionReport' => function ($q) use ($id, $from_date) {
                    $q->where('class_id', $id)
                        ->whereDate('created_at', Carbon::parse($from_date)->toDateString());
                }])
                ->withCount(['ijazaRevisionReport as revision_with_non_null_fields_count' => function ($q) {
                    $q->whereNotNull('revision_from_surat')
                        ->whereNotNull('revision_from_ayat')
                        ->whereNotNull('revision_to_surat')
                        ->whereNotNull('revision_to_ayat')
                        ->whereNotNull('revision_evaluation_id');
                }])
                ->withCount(['ijazaMemorizationReport as hefz_with_non_null_fields_count' => function ($q) {
                    $q->whereNotNull('hefz_from_surat')
                        ->whereNotNull('hefz_from_ayat')
                        ->whereNotNull('hefz_to_surat')
                        ->whereNotNull('hefz_to_ayat')
                        ->whereNotNull('ijazasanad_evaluation_id');
                }])
                ->with(['last_report'])
                ->with(['last_revision'])
                ->get();






            $incompleteMonthlyPlansCount = DB::table('ijazasanad_memorization_plans')
                ->where('class_id', $id)
                ->whereYear('start_date', Carbon::parse($from_date)->year)
                ->whereMonth('start_date', Carbon::parse($from_date)->month)
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->where('status', 'waiting_for_approval')->count();
            $completeMonthlyPlansCount = DB::table('ijazasanad_revision_plans')
                ->where('class_id', $id)
                ->whereYear('start_date', Carbon::parse($from_date)->year)
                ->whereMonth('start_date', Carbon::parse($from_date)->month)
                ->where('status', 'active')
                ->count();

            $noMonthlyPlanStudentsCount = Student::whereHas('class', function ($q) use ($id) {
                $q->where('class_id', $id);
            })->count();

            $hefz_evaluation_schema = EvaluationSchema::where('target', 'hefz')->first();
            $revision_evaluation_schema = EvaluationSchema::where('target', 'revision')->first();
            $hefz_valuation_options = $hefz_evaluation_schema->options()->get();
            $revision_valuation_options = $revision_evaluation_schema->options()->get();
            $teachers = $class->teachers;
            $attendanceOptions = AttendanceOption::orderByDesc('title')->get();

            return view('education::classes.reports.ijazasanad_create', compact('classProgramDetails','completeMonthlyPlansCount','colors', 'completeMonthlyPlansCount', 'noMonthlyPlanStudentsCount', 'incompleteMonthlyPlansCount', 'attendanceOptions', 'students', 'class', 'from_date', 'teachers', 'hefz_valuation_options', 'revision_valuation_options'));
        }
        else{


            


            DB::enableQueryLog();
            
            $students = Student::where('status','active')
                ->where(function($query) use ($id, $from_date) {
                    $query->whereHas('hefz_plans', function ($q) use ($id, $from_date) {
                        $q->where('class_id', $id)
                            ->where('status','active')
                            ->where(function ($q) use ($from_date) {
                                $q->whereNotNull('study_direction')
                                    ->whereNotNull('start_from_surat')
                                    ->whereNotNull('start_from_ayat')
                                    ->whereNotNull('to_surat')
                                    ->whereNotNull('to_ayat')
                                    ->where(function($q) use ($from_date) {
                                        $q->whereYear('start_date', Carbon::parse($from_date)->year)
                                          ->whereMonth('start_date', Carbon::parse($from_date)->month)
                                          ->orWhere(function($q) use ($from_date) {
                                            $q->whereYear('created_at', Carbon::parse($from_date)->year)
                                              ->whereMonth('created_at', Carbon::parse($from_date)->month);
                                        });
                                    });
                            });
                    })
                    ->orWhereHas('revision_plans', function ($q) use ($id, $from_date) {
                        $q->where('class_id', $id)
                            ->where('status','active')
                            ->where(function ($q) use ($from_date) {
                                $q->whereNotNull('study_direction')
                                    ->whereNotNull('start_from_surat')
                                    ->whereNotNull('start_from_ayat')
                                    ->whereNotNull('to_surat')
                                    ->whereNotNull('to_ayat')
                                    ->where(function($q) use ($from_date) {
                                        $q->whereYear('start_date', Carbon::parse($from_date)->year)
                                          ->whereMonth('start_date', Carbon::parse($from_date)->month)
                                          ->orWhere(function($q) use ($from_date) {
                                            $q->whereYear('created_at', Carbon::parse($from_date)->year)
                                              ->whereMonth('created_at', Carbon::parse($from_date)->month);
                                        });
                                    });
                            });
                    });
                })
                ->whereHas('joint_classes', function ($q) use ($id) {
                    return $q->where('class_id', $id);
                })
                ->orderBy('full_name', 'asc') // Order by full_name in ascending order
                ->with(['hefz_plans' => function ($q) use ($id, $from_date) {
                    $q->where('class_id', $id)
                    ->where('status','active')
                    ->where(function ($q) use ($from_date) {
                        $q->whereNotNull('study_direction')
                            ->whereNotNull('start_from_surat')->whereNotNull('start_from_ayat')
                            ->whereNotNull('to_surat')->whereNotNull('to_ayat')
                            ->whereYear('start_date', Carbon::parse($from_date)->year)
                            ->whereMonth('start_date', Carbon::parse($from_date)->month);
                    });
                }])
                ->with(['revision_plans' => function ($q) use ($id, $from_date) {
                    $q->where('class_id', $id)
                        ->where('status','active')
                        ->where(function ($q) use ($from_date) {
                            $q->whereNotNull('study_direction')
                                ->whereNotNull('start_from_surat')->whereNotNull('start_from_ayat')
                                ->whereNotNull('to_surat')->whereNotNull('to_ayat')
                                ->whereYear('start_date', Carbon::parse($from_date)->year)
                                ->whereMonth('start_date', Carbon::parse($from_date)->month);
                        });
                }])
                ->with(['hefz' => function ($q) use ($id, $from_date) {
                    $q->where('class_id', $id)

                        ->whereYear('created_at', Carbon::parse($from_date)->year)
                        ->whereMonth('created_at', Carbon::parse($from_date)->month)
                        ->whereDay('created_at', Carbon::parse($from_date)->day);
                }])
                ->with(['revision' => function ($q) use ($id, $from_date) {
                    $q->where('class_id',$id)
                        ->whereYear('created_at', Carbon::parse($from_date)->year)
                        ->whereMonth('created_at', Carbon::parse($from_date)->month)
                        ->whereDay('created_at', Carbon::parse($from_date)->day);
                }])
                // ->withCount(['revision as revision_with_non_null_fields_count' => function ($q) {
                    
                //     $q->whereNotNull('revision_from_surat')
                //         ->whereNotNull('revision_from_ayat')
                //         ->whereNotNull('revision_to_surat')
                //         ->whereNotNull('revision_to_ayat')
                //         ->whereNotNull('revision_evaluation_id')
                //         ->where(function($q) use ($from_date) {
                //             $q->whereYear('created_at', Carbon::parse($from_date)->year)
                //               ->whereMonth('created_at', Carbon::parse($from_date)->month);
                //         });
                // }])
                // ->withCount(['hefz as hefz_with_non_null_fields_count' => function ($q) {
                //     $q->whereNotNull('hefz_from_surat')
                //         ->whereNotNull('hefz_from_ayat')
                //         ->whereNotNull('hefz_to_surat')
                //         ->whereNotNull('hefz_to_ayat')
                //         ->whereNotNull('hefz_evaluation_id')
                //         ->where(function($q) use ($from_date) {
                //             $q->whereYear('created_at', Carbon::parse($from_date)->year)
                //               ->whereMonth('created_at', Carbon::parse($from_date)->month);
                //         });
                // }])
                ->with(['last_report'])
                ->with(['last_revision'])
                ->get();

            // dd(DB::getQueryLog());

            $incompleteMonthlyPlansCount = DB::table('student_hefz_plans')
                ->where('class_id', $id)
                ->whereYear('start_date', Carbon::parse($from_date)->year)
                ->whereMonth('start_date', Carbon::parse($from_date)->month)
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->where('status','waiting_for_approval')->count();
            $completeMonthlyPlansCount = DB::table('student_hefz_plans')
                ->where('class_id', $id)
                ->whereYear('start_date', Carbon::parse($from_date)->year)
                ->whereMonth('start_date', Carbon::parse($from_date)->month)
                ->where('status','active')
                ->count();

            $noMonthlyPlanStudentsCount = Student::whereHas('class', function ($q) use ($id) {
                $q->where('class_id', $id);
            })->count();

            $hefz_evaluation_schema = EvaluationSchema::where('target', 'hefz')->first();
            $revision_evaluation_schema = EvaluationSchema::where('target', 'revision')->first();
            $hefz_valuation_options = $hefz_evaluation_schema->options()->get();
            $revision_valuation_options = $revision_evaluation_schema->options()->get();
            $teachers = $class->teachers;
            $attendanceOptions = AttendanceOption::orderByDesc('title')->get();

            return view('education::classes.reports.create', compact('classProgramDetails','completeMonthlyPlansCount','colors', 'completeMonthlyPlansCount', 'noMonthlyPlanStudentsCount', 'incompleteMonthlyPlansCount', 'attendanceOptions', 'students', 'class', 'from_date', 'teachers', 'hefz_valuation_options', 'revision_valuation_options'));
        }
    }






    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    //public function store(Request $request)
    public function store(ClassReportStoreRequest $request)
    {
        try {

          


          

            
            \DB::beginTransaction();
            $field = $request->get('field');
            $field_value = $request->get('field_value');
            $organization_id = $request->get('organization_id');
            $table = $request->get('table');
            $teacher_id = $request->get('teacher_id');
            $subject_id = $request->get('subject_id');
            $report_id = $request->get('report_id');
            $class_time = Carbon::parse($request->get('class_time'));
            $hefz_plan_id = $request->get('hefz_plans_id');
            $revision_plan_id = $request->get('revision_plans_id');


            $now = Carbon::now();

            if (Carbon::parse($request->get('class_time'))->toDateString() != $now->toDateString()) {
                $dateTime = Carbon::parse($request->get('class_time'))->setTimeFrom($now);
            } else {
                $dateTime = $now;
            }



            // year month format
            $planYearMonth = $class_time;
            $dateMonthArray = explode('-', $planYearMonth);
            $year = $dateMonthArray[0];
            $month = $dateMonthArray[1];
            $planYearMonth = Carbon::createFromDate($year, $month, 1);
            $planYearMonth = $planYearMonth->format('Y-m');



            $hefz_from_surat = $request->get('hefz_from_surat');
            $hefz_from_ayat = $request->get('hefz_from_ayat');
            $hefz_to_surat = $request->get('hefz_to_surat');
            $hefz_to_ayat = $request->get('hefz_to_ayat');
            $hefz_evaluation_id = $request->get('hefz_evaluation_id');
            $hefz_evaluation_note = $request->get('hefz_evaluation_note');
            $attendance_id = $request->get('attendance_id');
            $revision_from_surat = $request->get('revision_from_surat');
            $revision_from_ayat = $request->get('revision_from_ayat');
            $revision_to_surat = $request->get('revision_to_surat');
            $revision_to_ayat = $request->get('revision_to_ayat');
            $revision_evaluation_id = $request->get('revision_evaluation_id');
            $revision_evaluation_note = $request->get('revision_evaluation_note');
            $student = Student::find($request->get('student_id'));

            // Validate essential data
            if (!$student) {
                throw new \Exception("Student not found with ID: " . $request->get('student_id'));
            }

            if (empty($attendance_id)) {
                throw new \Exception("Attendance ID is required");
            }

            if (strtolower($table) === "hefz") {
                $studentHefzReport = $student->hefz()
                    ->whereDate('created_at', $class_time->toDateString())
                    ->first();

                $studentHefzPlan = StudentHefzPlan::find($hefz_plan_id);

                // Get the class and its first program's ID dynamically with proper error handling
                $class = Classes::with('programs')->find($request->get('class_id'));
                if (!$class) {
                    throw new \Exception("Class not found with ID: " . $request->get('class_id'));
                }
                
                $program_id = $class->programs->first()->id ?? null;
                if (!$program_id) {
                    throw new \Exception("No program associated with class ID: " . $request->get('class_id'));
                }

                // Check if only attendance_id is provided and hefz fields are null/empty
                $hefzFieldsEmpty = (empty($hefz_from_surat) && empty($hefz_from_ayat) && empty($hefz_to_surat) && empty($hefz_to_ayat) && empty($hefz_evaluation_id));
                $onlyAttendanceProvided = !empty($attendance_id) && $hefzFieldsEmpty;

                // Initialize pages memorized
                $memorizedNumberofPages = 0;

                // Only calculate memorized pages if hefz fields are provided
                if (!$hefzFieldsEmpty && $studentHefzPlan) {
                    try {
                        // Determine the number of memorized pages based on the study direction
                        if ($studentHefzPlan->study_direction == 'backward') {
                            // Calling your stored procedure for backward direction
                            $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                $hefz_from_surat,
                                $hefz_from_ayat,
                                $hefz_to_surat,
                                $hefz_to_ayat
                            ]);
                            $memorizedNumberofPages = $result[0]->numberofPagesSum ?? 0;
                        } else {
                            // Calling your stored procedure for forward direction
                            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                $hefz_from_surat,
                                $hefz_from_ayat,
                                $hefz_to_surat,
                                $hefz_to_ayat
                            ]);
                            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                            $memorizedNumberofPages = $results[0]->number_of_pages_sum ?? 0;
                        }
                    } catch (\Exception $e) {
                        \Log::warning("Failed to calculate memorized pages: " . $e->getMessage());
                        $memorizedNumberofPages = 0; // Default to 0 if calculation fails
                    }
                }

                if (!$studentHefzReport) {
                    // If no record found for the given date, create a new one
                    $studentHefzReport = $student->hefz()->create([
                        'created_at' => $dateTime,
                        'report_date' => $class_time->toDateString(), // Set the new report_date field
                        'class_id' => $request->get('class_id'),
                        'student_id' => $student->id,
                        'teacher_id' => $teacher_id,
                        'program_id' => $program_id,
                        'organization_id' => $organization_id,
                        'created_by' => auth()->user()->id,
                        "hefz_from_surat" => !empty($hefz_from_surat) ? $hefz_from_surat : null,
                        "hefz_from_ayat" => !empty($hefz_from_ayat) ? $hefz_from_ayat : null,
                        "hefz_to_surat" => !empty($hefz_to_surat) ? $hefz_to_surat : null,
                        "hefz_to_ayat" => !empty($hefz_to_ayat) ? $hefz_to_ayat : null,
                        "hefz_evaluation_id" => !empty($hefz_evaluation_id) ? $hefz_evaluation_id : null,
                        "hefz_plan_id" => $hefz_plan_id,
                        "hefz_evaluation_note" => !empty($hefz_evaluation_note) ? $hefz_evaluation_note : null,
                        "attendance_id" => !empty($attendance_id) ? $attendance_id : null,
                        'pages_memorized' => $memorizedNumberofPages,
                    ], [
                        'timestamps' => false,
                    ]);
                } else {
                    // If a record exists for the given date, update it
                    $studentHefzReport->timestamps = false;

                    $studentHefzReport->update([
                        'class_id' => $request->get('class_id'),
                        'teacher_id' => $teacher_id,
                        'report_date' => $class_time->toDateString(), // Set the new report_date field
                        'hefz_from_surat' => !empty($hefz_from_surat) ? $hefz_from_surat : null,
                        'hefz_from_ayat' => !empty($hefz_from_ayat) ? $hefz_from_ayat : null,
                        'hefz_to_surat' => !empty($hefz_to_surat) ? $hefz_to_surat : null,
                        'hefz_to_ayat' => !empty($hefz_to_ayat) ? $hefz_to_ayat : null,
                        'hefz_evaluation_id' => !empty($hefz_evaluation_id) ? $hefz_evaluation_id : null,
                        'updated_at' => $now,
                        'hefz_plan_id' => $hefz_plan_id,
                        'hefz_evaluation_note' => !empty($hefz_evaluation_note) ? $hefz_evaluation_note : null,
                        'attendance_id' => !empty($attendance_id) ? $attendance_id : null,
                        'pages_memorized' => $memorizedNumberofPages,
                    ]);
                }

                // check if all required fields are filled for the report
                $checkIfAllRequiredColumnsHaveValues = !(is_null($studentHefzReport->hefz_from_surat) || is_null($studentHefzReport->hefz_from_ayat) || is_null($studentHefzReport->hefz_to_surat) || is_null($studentHefzReport->hefz_to_ayat));
                $checkIfAllRequiredColumnsHaveValuesforLastReportRecord = !(is_null($studentHefzReport->hefz_from_surat) || is_null($studentHefzReport->hefz_from_ayat) || is_null($studentHefzReport->hefz_to_surat) || is_null($studentHefzReport->hefz_to_ayat)|| is_null($studentHefzReport->hefz_evaluation_id));

                $studentHefzReport->refresh();
                if($checkIfAllRequiredColumnsHaveValuesforLastReportRecord)
                {
                    $created_at = Carbon::parse($studentHefzReport->created_at);

                    // update or insert the last revision record
                    \App\StudentLastMemorizationRecord::updateOrInsert(
                        ['student_id' => $student->id],  // Columns to check for existence
                        [
                            'memorization_year_month_day' => $created_at->toDateString(),
                            'from_surat' => $studentHefzReport->hefz_from_surat,
                            'from_ayat' => $studentHefzReport->hefz_from_ayat,
                            'to_surat' => $studentHefzReport->hefz_to_surat,
                            'to_ayat' => $studentHefzReport->hefz_to_ayat,
                        ]
                    );
                }
                if($checkIfAllRequiredColumnsHaveValues)
                {
                    $fromSuratJuz = $this->getMoshafJuzBySuratAndAyah($hefz_from_surat, $hefz_from_ayat);
                    $toSuratJuz = $this->getMoshafJuzBySuratAndAyah($hefz_to_surat, $hefz_to_ayat);
                    $this->updateStudentHefzReport($studentHefzReport, $fromSuratJuz, $toSuratJuz);
                    $studentHefzReport->refresh();
                }

                $attOption = AttendanceOption::find($attendance_id);
                if (!$attOption) {
                    throw new \Exception("Invalid attendance option ID: " . $attendance_id);
                }

                // TODO: update or create attendance
                StudentAttendance::updateOrCreate([
                    'class_report_id' => $studentHefzReport->id,
                    'student_id' => $student->id,
                ],
                    [
                        //                        'class_time' => $class_time->hour(Carbon::now()->hour)->minute(Carbon::now()->minute)->second(Carbon::now()->second)->toDateTimeString(),
                        'student_id' => $student->id,
                        'attendance_type' => $attOption->id,
                        'attendance_date' => $class_time->toDateString(),
                        'class_time' => $studentHefzReport->created_at,
                        'attendance' => $attOption->title,
                        'created_by' => auth()->user()->id]
                );

                // Handle StudentRevisionReport creation/update
                $studentRevisionReport = $student->revision()
                    ->whereDate('created_at', $class_time->toDateString())
                    ->first();

                if ($studentRevisionReport) {
                    // Update existing revision report
                    $studentRevisionReport->update([
                        'attendance_id' => $attendance_id,
                        'teacher_id' => $teacher_id,
                        'class_time' => $class_time->toDateString(),
                        'program_id' => $program_id,
                        'revision_plan_id' => $revision_plan_id,
                    ]);
                } else {
                    // Create new revision report - especially important when only attendance is provided
                    $studentRevisionReport = $student->revision()->create([
                        'created_at' => $dateTime,
                        'class_id' => $request->get('class_id'),
                        'class_time' => $class_time->toDateString(),
                        'student_id' => $student->id,
                        'teacher_id' => $teacher_id,
                        'program_id' => $program_id,
                        'organization_id' => $organization_id,
                        'created_by' => auth()->user()->id,
                        'attendance_id' => $attendance_id,
                        'revision_plan_id' => $revision_plan_id,
                        'revision_from_surat' => !empty($revision_from_surat) ? $revision_from_surat : null,
                        'revision_from_ayat' => !empty($revision_from_ayat) ? $revision_from_ayat : null,
                        'revision_to_surat' => !empty($revision_to_surat) ? $revision_to_surat : null,
                        'revision_to_ayat' => !empty($revision_to_ayat) ? $revision_to_ayat : null,
                        'revision_evaluation_id' => !empty($revision_evaluation_id) ? $revision_evaluation_id : null,
                        'revision_evaluation_note' => !empty($revision_evaluation_note) ? $revision_evaluation_note : null,
                        'pages_revised' => 0, // Default to 0 when only attendance is provided
                    ], [
                        'timestamps' => false,
                    ]);
                }

                // If only attendance is provided, ensure both reports are flagged as attendance-only entries
                if ($onlyAttendanceProvided) {
                    // Log that this is an attendance-only entry for both reports
                    \Log::info("Attendance-only entry created for student {$student->id} on {$class_time->toDateString()}", [
                        'hefz_report_id' => $studentHefzReport->id,
                        'revision_report_id' => $studentRevisionReport->id,
                        'attendance_id' => $attendance_id
                    ]);
                }
            }

            \DB::commit();
            $studentHefzPlan = $student->hefzPlanPerYearMonthPerClassPerStudent($student->id, $request->get('class_id'), $class_time->year, $class_time->month)->first();

            $surats = \App\MoshafSurah::whereIn('id',[$studentHefzPlan->start_from_surat, $studentHefzPlan->to_surat])->get();




            return response()->json(['message' => 'success', 'hefzReport' => $studentHefzReport, 'allRequiredFieldsFilled' => $checkIfAllRequiredColumnsHaveValues, 'attendanceId' => $attendance_id, 'surats' => $surats], 200);

        } catch (\Exception $exception) {
            \DB::rollBack();
            \Log::error($exception);



            $errorMessage = $exception->getMessage();
            return response()->json(compact('errorMessage'));
        }


    }

    public function getMoshafJuzBySuratAndAyah($surat, $ayat)
    {
        return MoshafJuz::where(function($query) use($surat) {
            $query->where('start_surah', '<=', $surat)
                ->where('end_surah', '>=', $surat);
        })
            ->where(function ($q) use($ayat) {
                $q->where('start_verse', '<=', $ayat)
                    ->orWhere('end_verse', '>=', $ayat);
            })
            ->first();
    }

    public function updateStudentHefzReport($studentHefzReport, $fromSuratJuz, $toSuratJuz)
    {
        $fromSuratJuzId = $fromSuratJuz ? $fromSuratJuz->juz : null;
        $toSuratJuzId = $toSuratJuz ? $toSuratJuz->juz : null;

        $studentHefzReport->update([
            'from_surat_juz_id' => $fromSuratJuzId,
            'to_surat_juz_id' => $toSuratJuzId,
        ]);

    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {

        $class = Classes::findOrFail($id);

        $programs = Program::all();

        return view('education::classes.show', compact('class', 'programs'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($class_id, $report_id)
    {
        $surats = MoshafSurah::all();

        $report = ClassReport::findOrFail($report_id);

        $class = Classes::findOrFail($report->class_id);
        $subject = [];
        $special_program_data = [];

        if ($report->subject_id == 0) {
            if ($report->program->setting['special_program_code']) {
                if ($report->program->setting['special_program_code'] = 'hefz') {
                    $special_program_data['data'] = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->evaluation($report);

                    $special_program_data['hefz_evaluation_schema'] = EvaluationSchema::where('target', 'hefz')->first();

                    $special_program_data['revision_evaluation_schema'] = EvaluationSchema::where('target', 'revision')->first();
                }
            }
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }

        $students = [];
        // return $special_program_data;
        // return $subject->contents;

        return view('education::classes.reports.edit', compact('class', 'report', 'students', 'subject', 'special_program_data', 'surats'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request)
    {
        $requestData = $request->all();
        // return $requestData;

        $report = ClassReport::findOrFail($request->report_id);
        if (isset($request->teacher_attendance) && $request->teacher_attendance == 'absent') {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->save();

            return redirect('workplace/education/classes/' . $report->class_id . '/reports');
        } elseif (isset($request->student_attendance)) {
            foreach ($request->student_attendance as $key => $value) {
                $attendance = new StudentAttendance();

                $attendance->organization_id = config('organization_id');
                $attendance->class_report_id = $report->id;
                $attendance->student_id = $key;
                $attendance->class_time = $report->class_time;
                $attendance->attendance = $value;
                $attendance->created_by = auth()->user()->id;

                $attendance->save();
                // $attendance->note = $report->;
            }

            $report->status = 'attendance_submited';
            $report->save();
        } elseif (isset($request->student_performance)) {
            if ($report->subject_id == 0) {
                if ($report->program->setting['special_program_code']) {
                    if ($report->program->setting['special_program_code'] = 'hefz') {
                        $report_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->evaluation($report);
                        foreach ($request->student_performance as $student_id => $result) {
                            if (isset($report_data[$student_id])) {
                                if (isset($report_data[$student_id]['hefz']) && $report_data[$student_id]['hefz'] && $result['hefz']) {
                                    $hefz_report = new StudentHefzReport();

                                    $hefz_report->student_id = $student_id;
                                    $hefz_report->organization_id = config('organization_id');
                                    $hefz_report->class_id = $report->class_id;
                                    $hefz_report->created_at = $report->class_time;
                                    $hefz_report->report_date = $report->class_time->toDateString(); // Set report_date
                                    $hefz_report->created_by = auth()->user()->id;

                                    $hefz_report->hefz_from_surat = $requestData['report'][$student_id]['hefz']['from_surat'];
                                    $hefz_report->hefz_from_ayat = $requestData['report'][$student_id]['hefz']['from_ayat'];
                                    $hefz_report->hefz_to_surat = $requestData['report'][$student_id]['hefz']['to_surat'];
                                    $hefz_report->hefz_to_ayat = $requestData['report'][$student_id]['hefz']['to_ayat'];

                                    $hefz_report->hefz_evaluation_id = $result['hefz'];
                                    $hefz_report->class_report_id = $report->id;

                                    $hefz_report->save();
                                }
                                if (isset($report_data[$student_id]['revision']) && $report_data[$student_id]['revision'] && $result['revision']) {
                                    $revision_report = new StudentRevisionReport();

                                    $revision_report->student_id = $student_id;
                                    $revision_report->organization_id = config('organization_id');
                                    $revision_report->class_id = $report->class_id;
                                    $revision_report->created_by = auth()->user()->id;
                                    $revision_report->created_at = $report->class_time;

                                    $revision_report->revision_from_surat = $requestData['report'][$student_id]['revision']['from_surat'];
                                    $revision_report->revision_from_ayat = $requestData['report'][$student_id]['revision']['from_ayat'];
                                    $revision_report->revision_to_surat = $requestData['report'][$student_id]['revision']['to_surat'];
                                    $revision_report->revision_to_ayat = $requestData['report'][$student_id]['revision']['to_ayat'];

                                    if (isset($requestData['report'][$student_id]['revision']['revision_note'])) {
                                        $revision_report->revision_evaluation_note = $requestData['report'][$student_id]['revision']['revision_note'];
                                    }
                                    if (isset($requestData['report'][$student_id]['revision']['revision_type'])) {
                                        $revision_report->revision_type = $requestData['report'][$student_id]['revision']['revision_type'];
                                    }

                                    $revision_report->revision_evaluation_id = $result['revision'];
                                    $revision_report->class_report_id = $report->id;

                                    $revision_report->save();
                                }
                            }
                        }

                        $report->status = 'completed';
                        $report->save();
                    }
                }
            }
        }

        Session::flash('flash_message', 'Class updated!');

        return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
    }


    /**
     * Update programs availabilty in class.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function programs(Request $request)
    {
        auth()->user()->can('edit class_programs');

        $id = $request->class_id;

        $class = Classes::findOrFail($id);

        $class->programs()->sync($request->class_programs);

        Session::flash('flash_message', 'Program updated!');

        if ($request->ajax()) {
            return response()->json(['status' => 'success']);
        }

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id, $hefzReportId)
    {


//        ClassReport::destroy($id);
        StudentHefzReport::destroy($hefzReportId);

        Session::flash('flash_message', 'Class deleted!');

        return response()->json('class report removed');
    }


    public function studentReport($student_id)
    {

        dd(333);
        return DB::table('student_attendances')
            ->leftJoin('class_reports', 'student_attendances.class_report_id', 'class_reports.id')
            ->leftJoin('student_hefz_report', 'student_attendances.class_report_id', '=', 'student_hefz_report.class_report_id')
            ->leftJoin('evaluation_schema_options as hefz_evaluation', 'hefz_evaluation.id', '=', 'student_hefz_report.hefz_evaluation_id')
            ->leftJoin('student_revision_report', 'student_attendances.class_report_id', '=', 'student_revision_report.class_report_id')
            ->leftJoin('evaluation_schema_options as revision_evaluation', 'revision_evaluation.id', '=', 'student_revision_report.revision_evaluation_id')
            ->select(
                'student_attendances.attendance',
                'student_attendances.note as student_attendance_note',
                'class_reports.*',
                'student_hefz_report.*',
                'hefz_evaluation.title as hefz_evaluation_title',
                'student_revision_report.*',
                'revision_evaluation.title as revision_evaluation_title'
            )
            ->where('student_attendances.student_id', $student_id)
            ->orderBy('class_reports.class_time')
            // ->select('users.*', 'contacts.phone', 'orders.price')
            ->get();
    }

    public function studentExpectedProgressPlan($student_id) // Hefz and Morja'ah Program
    {
    }






    private function getNextReportTime($last_report_time, $timetable, $class)
    {

        if (!$last_report_time) {
            $class_date = $timetable->start_at;
        } else {
            $class_date = Carbon::parse($last_report_time);
            $class_date = $class_date->addDay();
//            $class_date = $last_report_time->addDay();
        }

        while ($class->studentsAtDate($class_date)->count() < 1 && $class_date < Carbon::now()) {
            $class_date = Carbon::parse($class_date)->addDay();
        }

        while (!$timetable[strtolower(Carbon::parse($class_date)->format('D'))]) {
            $class_date = Carbon::parse($class_date)->addDay();
        }
        $class_date = Carbon::parse($class_date)->addDay();
        return $class_date;
    }

    private function errorNoTeacher($class_id)
    {


        $error_msg = "Opps! . Some Subjects In the Class Don't have Teacher!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }

    private function errorNoTimetable($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Timetable!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }


    // V2

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function prepareReport($class_id, $report_id)
    {
        $report = ClassReport::findOrFail($report_id);


        if ($report->status == 'completed') {
            return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
        }
        $surats = MoshafSurah::all();

        $suar = $surats->map(function ($surah) {
            return [
                'text' => $surah->name,
                'value' => $surah->id,
                'num_ayat' => $surah->num_ayat
            ];
        });


        $class = Classes::with('students.hefz')->with('students.revision')->findOrFail($report->class_id);


        $subject = [];
        $special_program_data = [];
        $special_program = null;

        $hefzEvaluationOptions = [];
        $revisionEvaluationOptions = [];

        if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
            $special_program_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->studentsEvaluation($report);

            $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $revisionEvaluationOptions = EvaluationSchema::where('target', 'revision')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $special_program = 'hefz';
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }
        // dd($special_program_data);
        $students = [];
        $lessons = [];
        // $class->students->map(function($student){
        //     return [

        //     ]
        // });

        foreach ($class->students as $student) {
            if ($special_program
                && $special_program == 'hefz'
                && isset($special_program_data[$student->id])
            ) {
                $students[$student->id] = $special_program_data[$student->id];
                $students[$student->id]['attandance'] = null;
                $students[$student->id]['evaluation'] = null;
            } else {
                $students[$student->id] = [
                    'attandance' => null,
                    'evaluation' => null,
                    'hefz' => [],
                    'revision' => [],
                    'lesson' => [
                        'id' => null,
                        'evaluations' => json_decode('{}')
                    ]
                ];
                if ($subject) {
                    $lessons = $subject->contents->map(function ($lesson) {
                        $evaluation_schems = [];
                        foreach ($lesson->evaluation_schemas as $evaluation) {
                            foreach ($evaluation->options as $option) {
                                $evaluation_schems[$evaluation->title][] = [
                                    'text' => $option->title,
                                    'value' => $option->id
                                ];
                            }
                        }

                        return [
                            'text' => $lesson->title,
                            'value' => $lesson->id,
                            'evaluation_schems' => $evaluation_schems
                        ];
                    });
                }
            }
        }


        return view('education::classes.reports.v3.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reports.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reportsBackup.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
    }

    public function storeTempReport($class_id, $report_id)
    {

        $report = ClassReport::findOrFail($report_id);
        $report->temp_data = json_encode(request()->except('_token'));
        $report->save();
    }

    public function storeFinalReport(Request $request, $class_id, $report_id)
    {
        // dd($request->all());
        $this->validate($request, [
            'students.*' => 'required',
            'teacher_attended' => 'required'
        ]);

        $report = ClassReport::findOrFail($report_id);

        StudentAttendance::where('class_report_id', $report->id)->delete();
        StudentHefzReport::where('class_report_id', $report->id)->delete();
        StudentRevisionReport::where('class_report_id', $report->id)->delete();


        $requestData = $request->all();

        if (!$request->teacher_attended) {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->temp_data = '';
            $report->save();

            return response()->json(['status' => 'completed'], 200);
        }

        foreach ($request->students as $studentID => $studentReport) {
            $attendance = new StudentAttendance();

            $attendance->organization_id = config('organization_id');
            $attendance->class_report_id = $report->id;
            $attendance->student_id = $studentID;
            $attendance->class_time = $report->class_time;
            $attendance->attendance = $studentReport['attandance'];
            $attendance->created_by = auth()->user()->id;
            $attendance->save();

            if (in_array($attendance->attendance, ['on_time', 'late'])) {
                if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
                    if (isset($studentReport['hefz']) && $studentReport['hefz']) {
                        $hefz_report = new StudentHefzReport();
                        $hefz_report->student_id = $studentID;
                        $hefz_report->organization_id = config('organization_id');
                        $hefz_report->class_id = $report->class_id;
                        $hefz_report->created_at = $report->class_time;
                        $hefz_report->created_by = auth()->user()->id;

                        $hefz_report->hefz_from_surat = $studentReport['hefz']['from_surat'];
                        $hefz_report->hefz_from_ayat = $studentReport['hefz']['from_ayat'];
                        $hefz_report->hefz_to_surat = $studentReport['hefz']['to_surat'];
                        $hefz_report->hefz_to_ayat = $studentReport['hefz']['to_ayat'];

                        $hefz_report->hefz_evaluation_id = $studentReport['hefz']['evaluation'];
                        $hefz_report->class_report_id = $report->id;

                        $hefz_report->save();
                    }
                    if (isset($studentReport['revision']) && $studentReport['revision']) {
                        $revision_report = new StudentRevisionReport();

                        $revision_report->student_id = $studentID;
                        $revision_report->organization_id = config('organization_id');
                        $revision_report->class_id = $report->class_id;
                        $revision_report->created_by = auth()->user()->id;
                        $revision_report->created_at = $report->class_time;


                        $revision_report->revision_from_surat = $studentReport['revision']['from_surat'];
                        $revision_report->revision_from_ayat = $studentReport['revision']['from_ayat'];
                        $revision_report->revision_to_surat = $studentReport['revision']['to_surat'];
                        $revision_report->revision_to_ayat = $studentReport['revision']['to_ayat'];

                        if (isset($studentReport['revision']['revision_note'])) {
                            $revision_report->revision_evaluation_note = $studentReport['revision']['revision_note'];
                        }
                        if (isset($studentReport['revision']['revision_type'])) {
                            $revision_report->revision_type = $studentReport['revision']['revision_type'];
                        }


                        $revision_report->revision_evaluation_id = $studentReport['revision']['evaluation'];

                        $revision_report->class_report_id = $report->id;

                        $revision_report->save();
                    }
                } else {
                    // Todo: ADD REPORT OF NORMAL PROGRAM
                    if (isset($studentReport['lesson']) && $studentReport['lesson']) {
                        $lesson_report = new LessonReport();

                        $lesson_report->student_id = $studentID;
                        $lesson_report->organization_id = config('organization_id');
                        $lesson_report->class_id = $report->class_id;
                        $lesson_report->created_by = auth()->user()->id;
                        $lesson_report->class_time = $report->class_time;


                        $lesson_report->lesson_id = $studentReport['lesson']['id'];

                        if (isset($studentReport['lesson']['note'])) {
                            $lesson_report->note = $studentReport['revision']['note'];
                        }

                        $lesson_report->class_report_id = $report->id;
                        $lesson_report->save();
                        foreach ($studentReport['lesson']['evaluations'] as $label => $evaluation_option) {
                            $lesson_report->evaluations()->create(['evaluation_option_id' => $evaluation_option]);
                        }
                    }
                }
            }
        }
        $report->temp_data = '';
        $report->status = 'completed';
        $report->save();
        return response()->json(['status' => 'completed'], 200);
    }
}
