/* Nouraniyah Mobile Report Styles - Optimized for Performance */

/* Mobile/Tablet View Styles */
.mobile-report-container {
    display: none;
    padding: 10px;
    background-color: #f8f9fa;
    max-width: 100%;
    margin: 0 auto;
}

.mobile-search-container {
    margin-bottom: 15px;
    background: white;
    padding: 12px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mobile-search-container .form-control {
    height: 45px;
    font-size: 16px;
    border-radius: 6px;
}

.mobile-students-container {
    margin-top: 15px;
}

.mobile-student-accordion {
    margin-bottom: 12px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.mobile-student-header {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.mobile-student-header:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    color: white;
    text-decoration: none;
}

.mobile-student-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mobile-student-avatar {
    margin-right: 15px;
}

.mobile-avatar-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid white;
    object-fit: cover;
}

.mobile-student-details {
    flex: 1;
}

.mobile-student-name {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: bold;
    color: white;
}

.mobile-level-badge {
    font-size: 0.75em;
    border-radius: 4px;
    padding: 2px 8px;
    font-weight: bold;
    display: inline-block;
    border: 2px solid;
    background-color: rgba(255,255,255,0.2);
}

.mobile-level-badge-one {
    border-color: white;
    color: white;
}

.mobile-level-badge-two {
    border-color: yellow;
    color: yellow;
}

.mobile-level-badge-three {
    border-color: #90EE90;
    color: #90EE90;
}

.mobile-expand-icon {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.mobile-student-content {
    background: white;
    padding: 15px;
}

.mobile-section {
    margin-bottom: 18px;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    position: relative;
}

.mobile-nouranya-section {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(32, 201, 151, 0.05));
    border-left: 4px solid #28a745;
}

.mobile-attendance-section {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 133, 27, 0.05));
    border-left: 4px solid #ffc107;
}

.mobile-section-title {
    margin: 0 0 12px 0;
    font-size: 15px;
    font-weight: bold;
    color: #495057;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mobile-section-title i {
    margin-right: 8px;
}

.mobile-field-group {
    margin-bottom: 12px;
    position: relative;
    z-index: 1;
}

.mobile-field-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.mobile-field-group .form-control {
    height: 44px;
    font-size: 16px;
    border-radius: 5px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    width: 100%;
    padding: 8px 12px;
}

.mobile-field-group .form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.mobile-lesson-row {
    margin-bottom: 15px;
    clear: both;
    overflow: hidden;
}

.mobile-lesson-row .col-xs-6 {
    padding-left: 7px;
    padding-right: 7px;
    float: left;
    width: 50%;
}

.mobile-nouranya-remarks-btn {
    width: 100%;
    height: 44px;
    font-size: 14px;
    border-radius: 5px;
    transition: all 0.3s ease;
    background-color: #d9534f;
    border-color: #d43f3a;
    color: white;
}

.mobile-nouranya-remarks-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    background-color: #c9302c;
    border-color: #ac2925;
    color: white;
}

/* Loading states */
.mobile-loading {
    position: relative;
    pointer-events: none;
}

.mobile-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    z-index: 10;
    border-radius: 8px;
}

.mobile-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #28a745;
    border-radius: 50%;
    animation: mobile-spin 1s linear infinite;
    z-index: 11;
}

@keyframes mobile-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Media queries for responsive design */
@media (min-width: 1025px) {
    .mobile-report-container {
        display: none !important;
    }
}

@media (max-width: 1024px) {
    #nouranyaReportTable {
        display: none !important;
    }
    .mobile-report-container {
        display: block !important;
    }
}

@media (max-width: 480px) {
    .mobile-report-container {
        padding: 8px;
    }
    
    .mobile-student-content {
        padding: 12px;
    }
    
    .mobile-section {
        padding: 10px;
        margin-bottom: 15px;
    }
    
    .mobile-student-name {
        font-size: 15px;
    }
    
    .mobile-field-group .form-control {
        height: 48px;
        font-size: 16px;
    }
}

/* Select2 mobile optimizations */
.select2-container {
    width: 100% !important;
    z-index: 9999 !important;
}

.select2-container .select2-selection--single {
    height: 44px !important;
    border-radius: 5px !important;
    border: 1px solid #ced4da !important;
    font-size: 16px !important;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    line-height: 42px !important;
    padding-left: 12px !important;
    color: #333 !important;
}

.select2-dropdown {
    z-index: 99999 !important;
    border-radius: 5px !important;
    border: 1px solid #ced4da !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

.select2-results__option {
    padding: 12px !important;
    font-size: 16px !important;
    line-height: 1.4 !important;
}
