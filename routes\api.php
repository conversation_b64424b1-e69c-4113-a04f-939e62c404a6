<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\ClassTimetableController;
use App\Http\Controllers\Api\SweetNavigationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

// Include mobile report API routes
require __DIR__ . '/api_mobile_reports.php';

// Class timetable endpoint for calendar
Route::get('/class-timetable', [ClassTimetableController::class, 'getClassTimetable'])
    ->middleware(['throttle:60,1']); // Rate limiting: 60 requests per minute

// Program data endpoint for class calendar
Route::get('/programs', [ClassTimetableController::class, 'getPrograms'])
    ->middleware(['throttle:60,1']); // Rate limiting: 60 requests per minute

// Registration API endpoints (commented out - controller doesn't exist)
// Route::get('/check-username', [RegistrationController::class, 'checkUsername']);
// Route::get('/check-email', [RegistrationController::class, 'checkEmail']);

/*
|--------------------------------------------------------------------------
| Sweet Navigation API Routes
|--------------------------------------------------------------------------
|
| These routes provide sample endpoints for testing the Sweet Navigation
| component. They demonstrate the expected JSON response format and can
| be used as templates for implementing navigation endpoints.
|
*/

// Sweet Navigation sample endpoints
Route::prefix('sweet-navigation')->group(function () {
    // Classes navigation endpoint
    Route::get('/classes', [SweetNavigationController::class, 'getClassesNavigation'])
        ->name('api.sweet-navigation.classes')
        ->middleware(['throttle:60,1']);

    // Students navigation endpoint
    Route::get('/students', [SweetNavigationController::class, 'getStudentsNavigation'])
        ->name('api.sweet-navigation.students')
        ->middleware(['throttle:60,1']);

    // Teachers navigation endpoint
    Route::get('/teachers', [SweetNavigationController::class, 'getTeachersNavigation'])
        ->name('api.sweet-navigation.teachers')
        ->middleware(['throttle:60,1']);

    // Error testing endpoint
    Route::get('/error-test', [SweetNavigationController::class, 'getErrorTest'])
        ->name('api.sweet-navigation.error-test')
        ->middleware(['throttle:60,1']);

    // Empty results testing endpoint
    Route::get('/empty', [SweetNavigationController::class, 'getEmptyNavigation'])
        ->name('api.sweet-navigation.empty')
        ->middleware(['throttle:60,1']);
});
