<?php

namespace Modules\Education\Http\Controllers\Api;

use App\Classes;
use App\Student;
use App\StudentNouranyaReport;
use App\StudentNouranyaPlan;
use App\ProgramLevelLesson;
use App\AttendanceOption;
use App\EvaluationSchemaOption;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * MobileReportController handles AJAX requests for mobile Nouraniyah reporting.
 * 
 * Purpose: Provides fast, cached API endpoints for mobile daily reporting interface
 * to enable teachers to efficiently record student progress on smartphones and tablets.
 * Side effects: Caches lesson data for 30 minutes, logs performance metrics,
 * updates student reports and attendance records in real-time.
 * Performance: Implements aggressive caching, query optimization, and bulk operations
 * to achieve sub-second response times for mobile interactions.
 */
final class MobileReportController extends Controller
{
    /**
     * Get available lessons for a student based on their program level and plan.
     */
    public function getStudentLessons(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|integer|exists:students,id',
            'class_id' => 'required|integer|exists:classes,id',
            'from_date' => 'required|date'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid parameters'], 400);
        }

        $studentId = $request->student_id;
        $classId = $request->class_id;
        $fromDate = $request->from_date;

        // Cache key for student lessons
        $cacheKey = "student_lessons_{$studentId}_{$classId}_{$fromDate}";

        $data = Cache::remember($cacheKey, 1800, function () use ($studentId, $classId, $fromDate) {
            $student = Student::with([
                'nouranya_plans' => function ($q) use ($classId, $fromDate) {
                    $q->where('class_id', $classId)
                        ->where('status', 'active')
                        ->whereYear('start_date', Carbon::parse($fromDate)->year)
                        ->whereMonth('start_date', Carbon::parse($fromDate)->month);
                },
                'studentProgramLevels.programlevel'
            ])->find($studentId);

            if (!$student || !$student->nouranya_plans->first()) {
                return ['lessons' => [], 'current_from_lesson' => null];
            }

            $plan = $student->nouranya_plans->first();
            $programLevel = $student->studentProgramLevels->first();

            if (!$programLevel) {
                return ['lessons' => [], 'current_from_lesson' => null];
            }

            // Get lessons based on program level
            $lessons = ProgramLevelLesson::where('program_level_id', $programLevel->level_id)
                ->whereBetween('lesson_no', [$plan->from_lesson ?? 1, $plan->to_lesson ?? 100])
                ->orderBy('lesson_no', 'asc')
                ->get(['id', 'lesson_no', 'name'])
                ->map(function ($lesson) {
                    return [
                        'id' => $lesson->lesson_no,
                        'name' => "Lesson {$lesson->lesson_no}: {$lesson->name}"
                    ];
                });

            // Get current selection from today's report if exists
            $currentReport = StudentNouranyaReport::where('student_id', $studentId)
                ->where('class_id', $classId)
                ->whereDate('created_at', $fromDate)
                ->first();

            return [
                'lessons' => $lessons,
                'current_from_lesson' => $currentReport->from_lesson ?? null,
                'current_to_lesson' => $currentReport->to_lesson ?? null
            ];
        });

        return response()->json($data);
    }

    /**
     * Get available "To" lessons based on selected "From" lesson.
     */
    public function getToLessons(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|integer|exists:students,id',
            'from_lesson_id' => 'required|integer',
            'class_id' => 'required|integer|exists:classes,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid parameters'], 400);
        }

        $studentId = $request->student_id;
        $fromLessonId = $request->from_lesson_id;
        $classId = $request->class_id;

        $cacheKey = "to_lessons_{$studentId}_{$fromLessonId}_{$classId}";

        $lessons = Cache::remember($cacheKey, 1800, function () use ($studentId, $fromLessonId, $classId) {
            $student = Student::with([
                'nouranya_plans' => function ($q) use ($classId) {
                    $q->where('class_id', $classId)->where('status', 'active');
                },
                'studentProgramLevels'
            ])->find($studentId);

            if (!$student || !$student->nouranya_plans->first()) {
                return [];
            }

            $plan = $student->nouranya_plans->first();
            $programLevel = $student->studentProgramLevels->first();

            if (!$programLevel) {
                return [];
            }

            // Get lessons from selected "from" lesson to plan's "to" lesson
            return ProgramLevelLesson::where('program_level_id', $programLevel->level_id)
                ->where('lesson_no', '>=', $fromLessonId)
                ->where('lesson_no', '<=', $plan->to_lesson ?? 100)
                ->orderBy('lesson_no', 'asc')
                ->get(['id', 'lesson_no', 'name'])
                ->map(function ($lesson) {
                    return [
                        'id' => $lesson->lesson_no,
                        'name' => "Lesson {$lesson->lesson_no}: {$lesson->name}"
                    ];
                });
        });

        return response()->json(['lessons' => $lessons]);
    }

    /**
     * Calculate pages between two lessons.
     */
    public function calculatePages(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'from_lesson_id' => 'required|integer',
            'to_lesson_id' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid parameters'], 400);
        }

        $fromLessonId = $request->from_lesson_id;
        $toLessonId = $request->to_lesson_id;

        // Simple calculation - in real implementation, this would be more complex
        $pages = max(0, $toLessonId - $fromLessonId + 1);

        return response()->json(['pages' => $pages]);
    }

    /**
     * Update student attendance with optimized database operations.
     */
    public function updateAttendance(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|integer|exists:students,id',
            'attendance_id' => 'required|integer|exists:attendance_options,id',
            'class_id' => 'required|integer|exists:classes,id',
            'from_date' => 'required|date'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid parameters'], 400);
        }

        try {
            DB::beginTransaction();

            $studentId = $request->student_id;
            $attendanceId = $request->attendance_id;
            $classId = $request->class_id;
            $fromDate = $request->from_date;

            // Update or create student report
            $report = StudentNouranyaReport::updateOrCreate(
                [
                    'student_id' => $studentId,
                    'class_id' => $classId,
                    'created_at' => Carbon::parse($fromDate)->startOfDay()
                ],
                [
                    'attendance_id' => $attendanceId,
                    'teacher_id' => auth()->id(),
                    'updated_at' => now()
                ]
            );

            DB::commit();

            // Clear related caches
            $this->clearStudentCaches($studentId, $classId, $fromDate);

            Log::info('Mobile attendance updated', [
                'student_id' => $studentId,
                'attendance_id' => $attendanceId,
                'teacher_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Attendance updated successfully',
                'report_id' => $report->id
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update attendance', [
                'error' => $e->getMessage(),
                'student_id' => $request->student_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update attendance'
            ], 500);
        }
    }

    /**
     * Update student evaluation.
     */
    public function updateEvaluation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|integer|exists:students,id',
            'evaluation_id' => 'required|integer|exists:evaluation_schema_options,id',
            'class_id' => 'required|integer|exists:classes,id',
            'from_date' => 'required|date'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid parameters'], 400);
        }

        try {
            DB::beginTransaction();

            $report = StudentNouranyaReport::where('student_id', $request->student_id)
                ->where('class_id', $request->class_id)
                ->whereDate('created_at', $request->from_date)
                ->first();

            if (!$report) {
                return response()->json([
                    'success' => false,
                    'message' => 'Report not found. Please set attendance first.'
                ], 404);
            }

            $report->update([
                'nouranya_evaluation_id' => $request->evaluation_id,
                'updated_at' => now()
            ]);

            DB::commit();

            $this->clearStudentCaches($request->student_id, $request->class_id, $request->from_date);

            return response()->json([
                'success' => true,
                'message' => 'Evaluation updated successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update evaluation', [
                'error' => $e->getMessage(),
                'student_id' => $request->student_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update evaluation'
            ], 500);
        }
    }

    /**
     * Get student remarks for the specified date.
     */
    public function getStudentRemarks(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|integer|exists:students,id',
            'class_id' => 'required|integer|exists:classes,id',
            'from_date' => 'required|date'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid parameters'], 400);
        }

        $report = StudentNouranyaReport::where('student_id', $request->student_id)
            ->where('class_id', $request->class_id)
            ->whereDate('created_at', $request->from_date)
            ->first();

        return response()->json([
            'remarks' => $report->remarks ?? '',
            'last_updated' => $report ? $report->updated_at->format('Y-m-d H:i:s') : null
        ]);
    }

    /**
     * Save student remarks.
     */
    public function saveRemarks(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|integer|exists:students,id',
            'remarks' => 'nullable|string|max:1000',
            'class_id' => 'required|integer|exists:classes,id',
            'from_date' => 'required|date'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid parameters'], 400);
        }

        try {
            DB::beginTransaction();

            $report = StudentNouranyaReport::where('student_id', $request->student_id)
                ->where('class_id', $request->class_id)
                ->whereDate('created_at', $request->from_date)
                ->first();

            if (!$report) {
                return response()->json([
                    'success' => false,
                    'message' => 'Report not found. Please set attendance first.'
                ], 404);
            }

            $report->update([
                'remarks' => $request->remarks,
                'updated_at' => now()
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Remarks saved successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to save remarks', [
                'error' => $e->getMessage(),
                'student_id' => $request->student_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to save remarks'
            ], 500);
        }
    }

    /**
     * Log performance metrics for monitoring.
     */
    public function logPerformance(Request $request): JsonResponse
    {
        $metrics = $request->only(['load_time', 'interaction_time', 'student_count', 'action']);
        
        Log::info('Mobile report performance', array_merge($metrics, [
            'user_id' => auth()->id(),
            'timestamp' => now(),
            'user_agent' => $request->userAgent()
        ]));

        return response()->json(['success' => true]);
    }

    /**
     * Clear student-related caches for real-time updates.
     */
    private function clearStudentCaches(int $studentId, int $classId, string $fromDate): void
    {
        $cacheKeys = [
            "student_lessons_{$studentId}_{$classId}_{$fromDate}",
            "nouranya_report_data_{$classId}_{$fromDate}",
            "student_report_{$studentId}_{$fromDate}"
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }
}
