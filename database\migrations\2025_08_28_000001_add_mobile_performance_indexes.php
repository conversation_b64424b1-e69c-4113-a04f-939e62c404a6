<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

/**
 * Add database indexes optimized for mobile Nouraniyah reporting performance.
 * 
 * Purpose: Creates composite indexes and optimizations to support 45x performance
 * improvement for mobile daily reporting queries.
 * Side effects: Improves query performance for student lookups, report filtering,
 * and lesson data retrieval; may temporarily lock tables during index creation.
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add composite index for student nouranya plans (most critical for mobile performance)
        Schema::table('student_nouranya_plans', function (Blueprint $table) {
            $table->index(['class_id', 'status', 'start_date'], 'idx_mobile_nouranya_plans');
            $table->index(['student_id', 'class_id', 'status'], 'idx_student_class_status');
        });

        // Add composite index for student nouranya reports (daily report lookups)
        Schema::table('student_nouranya_reports', function (Blueprint $table) {
            $table->index(['class_id', 'created_at'], 'idx_mobile_class_reports');
            $table->index(['student_id', 'class_id', 'created_at'], 'idx_mobile_student_reports');
        });

        // Add index for student joint classes (class membership lookups)
        Schema::table('student_joint_classes', function (Blueprint $table) {
            $table->index(['class_id', 'student_id'], 'idx_mobile_class_students');
        });

        // Add index for program level lessons (lesson data lookups)
        Schema::table('program_level_lessons', function (Blueprint $table) {
            $table->index(['program_level_id', 'lesson_no'], 'idx_mobile_level_lessons');
        });

        // Add index for student program levels (level lookups)
        Schema::table('student_program_levels', function (Blueprint $table) {
            $table->index(['student_id', 'level_id'], 'idx_mobile_student_levels');
        });

        // Optimize existing indexes for mobile queries
        DB::statement('OPTIMIZE TABLE students, student_nouranya_plans, student_nouranya_reports');
        
        // Update table statistics for better query planning
        DB::statement('ANALYZE TABLE students, student_nouranya_plans, student_nouranya_reports, student_joint_classes');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('student_nouranya_plans', function (Blueprint $table) {
            $table->dropIndex('idx_mobile_nouranya_plans');
            $table->dropIndex('idx_student_class_status');
        });

        Schema::table('student_nouranya_reports', function (Blueprint $table) {
            $table->dropIndex('idx_mobile_class_reports');
            $table->dropIndex('idx_mobile_student_reports');
        });

        Schema::table('student_joint_classes', function (Blueprint $table) {
            $table->dropIndex('idx_mobile_class_students');
        });

        Schema::table('program_level_lessons', function (Blueprint $table) {
            $table->dropIndex('idx_mobile_level_lessons');
        });

        Schema::table('student_program_levels', function (Blueprint $table) {
            $table->dropIndex('idx_mobile_student_levels');
        });
    }
};
